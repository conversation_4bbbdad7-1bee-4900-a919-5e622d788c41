# 需求文档

## 文档信息

- **功能名称**: 退货对接SAP
- **版本**: 1.0
- **日期**: 2025-07-22
- **作者**: Kiro AI助手
- **利益相关者**: OMS开发团队, SAP集成团队, 业务运营团队

## 概述

Miele项目需要实现退货订单与SAP系统的自动化对接，重点关注miele中的业务逻辑实现。该功能将建立完整的退货业务流程，包括审核、质检、补差等环节，并通过指定的接口与SAP系统进行数据同步。

当前退货流程缺乏系统化的管理界面和自动化的SAP集成，需要建立标准化的工作流程来处理不同类型的退货场景。

### 功能概要
实现Miele退货订单的完整业务流程管理和SAP系统数据同步

### 业务价值
- 建立标准化的退货审核和质检流程
- 支持七天无理由退款的差异化处理
- 实现补差订单的自动化生成和管理
- 提供完整的退货工单管理界面
- 确保退货数据与SAP系统的准确同步

### 范围
**包含范围:**
- reship_wo表的数据管理和界面展示
- 退货审核和质检业务流程
- 七天无理由退款的特殊处理逻辑
- 平台强制退款的自动化处理
- 补差订单的生成和管理
- SAP接口调用：miele.qimen.esb.reship.add
- SAP回调接口：miele.reship.addCallback

**排除范围:**
- 退货申请的前台界面
- 电商平台的审核流程
- SAP系统内部的业务逻辑
- 第三方物流系统的直接集成

## 需求

### 需求1: 退货工单管理界面

**用户故事:** 作为客服人员，我希望有一个完整的退货工单管理界面，以便能够查看、审核和处理退货订单。

#### 验收标准

1. 当 退货单生成 时 系统 应当 通过service注册将数据更新到reship_wo表中
2. 当 访问miele应用 时 系统 应当 使用框架的finder列表机制展示reship_wo数据
3. 当 查看工单列表 时 系统 应当 根据同步状态展示不同的标签页面
4. 当 查看工单详情 时 系统 应当 包含商品明细和操作记录
5. 当 查看操作列 时 系统 应当 显示审核和质检按钮（需先审核后质检）

#### 附加详情
- **优先级**: 高
- **复杂度**: 中等
- **依赖**: 框架finder机制, service注册功能
- **假设**: reship_wo表结构已定义

### 需求2: 退货审核流程

**用户故事:** 作为客服人员，我希望能够对退货单进行审核，并根据不同情况选择相应的处理方式。

#### 验收标准

1. 当 点击审核按钮 时 系统 应当 弹出审核操作框，链接为：`index.php?app=miele&ctl=admin_reship&act=audit&id={工单ID}`
2. 当 审核操作框显示 时 系统 应当 包含退货原因（7天无理由是/否）和取件类型选项
3. 如果 选择7天无理由退货 则 系统 应当 提供取件类型选择：1（上门质检）、2（消费者自行寄回）、3（上门取件）
4. 如果 选择上门取件 则 系统 应当 要求填写取件地址、联系方式和物流费用信息
5. 当 需要支付物流费用 时 系统 应当 生成补差订单（sap_so单），物料编码为固定的运费编码
6. 如果 是平台强制退款 则 系统 应当 默认设置为上门取件且无需质检，只需填写取件地址
7. 如果 是小家电 则 系统 应当 默认设置为消费者自行寄回

#### 附加详情
- **优先级**: 高
- **复杂度**: 高
- **依赖**: 补差订单生成功能
- **假设**: 运费编码已预定义

### 需求3: 质检流程管理

**用户故事:** 作为质检人员，我希望能够对需要质检的退货商品进行质检操作，并记录质检结果。

#### 验收标准

1. 当 审核选择上门质检 时 系统 应当 在审核完成后显示质检操作按钮，链接为：`index.php?app=miele&ctl=admin_reship&act=quality_check&id={工单ID}`
2. 当 点击质检按钮 时 系统 应当 显示质检界面表单
3. 当 质检界面显示 时 系统 应当 包含质检状态（通过/不通过）选项
4. 如果 质检通过 则 系统 应当 要求填写消费者联系方式（姓名、电话、地址）和物流费用信息
5. 当 质检保存且需要物流费用 时 系统 应当 生成补差订单，物料编码为固定的运费编码
6. 如果 质检不通过 则 系统 应当 不需要填写任何额外信息

#### 附加详情
- **优先级**: 中等
- **复杂度**: 中等
- **依赖**: 审核流程完成
- **假设**: 质检标准已定义

### 需求4: SAP数据同步

**用户故事:** 作为系统管理员，我希望退货数据能够自动同步到SAP系统，确保数据一致性。

#### 验收标准

1. 当 入库状态为已入库 时 系统 应当 允许将WO单据推送给SAP
2. 当 推送SAP 时 系统 应当 每5分钟轮询调用miele.qimen.esb.reship.add接口
3. 当 SAP返回成功 时 系统 应当 通过miele.reship.addCallback接口接收reship单号
4. 当 接收到回调 时 系统 应当 保存reship单号并更新同步状态

#### 附加详情
- **优先级**: 高
- **复杂度**: 高
- **依赖**: SAP接口可用性, 入库状态准确性
- **假设**: SAP系统能正常响应接口调用

### 需求5: 平台强制退款处理

**用户故事:** 作为客服人员，我希望系统能自动识别和处理平台强制退款，简化操作流程。

#### 验收标准

1. 当 退货单为平台强制退款 时 系统 应当 识别flag_type包含ome_reship_const::_QZ_REFUND_CODE
2. 当 生成强制退款WO单 时 系统 应当 立即发送强制退款邮件给EC
3. 当 审核强制退款 时 系统 应当 要求填写取件地址（消费者联系方式：姓名、电话、地址）
4. 当 取件地址填写完成 时 系统 应当 默认设置为上门取件且无需质检
5. 当 强制退款审核完成 时 系统 应当 客服线下邮件通知上门取件

#### 附加详情
- **优先级**: 中等
- **复杂度**: 中等
- **依赖**: 邮件服务可用性
- **假设**: 平台强制退款标识准确可靠

### 需求6: 补差订单管理

**用户故事:** 作为客服人员，我希望能够手动处理补差订单，确保退款金额的准确性。

#### 验收标准

1. 当 SAP同步成功且状态为succ 时 系统 应当 在操作列显示补差按钮，链接为：`index.php?app=miele&ctl=admin_reship&act=bucha&id={工单ID}`
2. 当 点击补差按钮 时 系统 应当 弹出补差操作框
3. 当 补差框显示 时 系统 应当 包含订单明细、提供checkbox框、销售物料、基础物料（只显示有效的）、基础物料名称、实付、补差金额、补差金额（小计）
4. 当 提交补差 时 系统 应当 验证补差金额与行明细小计的一致性，验证行明细是否为订单行明细
5. 当 验证通过 时 系统 应当 生成补差SAP_SO单推送给SAP

#### 附加详情
- **优先级**: 中等
- **复杂度**: 高
- **依赖**: SAP同步状态, 订单明细数据准确性
- **假设**: 补差计算规则已明确定义

## 非功能性需求

### 性能需求
- 当 系统处理退货数据同步 时 响应时间 应当 不超过30秒
- 当 接口调用高峰期 时 系统 应当 支持每分钟至少100次接口调用

### 安全需求
- 当 调用SAP接口 时 系统 应当 使用安全的认证机制
- 当 传输敏感数据 时 系统 应当 使用加密传输协议

### 可用性需求
- 当 操作员查看退货状态 时 界面 应当 清晰显示同步状态和错误信息
- 当 系统出现异常 时 错误信息 应当 提供明确的解决指导

### 可靠性需求
- 当 接口调用失败 时 系统 应当 自动重试最多3次
- 当 系统重启后 时 未完成的同步任务 应当 自动恢复处理

## 约束和假设

### 技术约束
- 必须通过塔内ESB → 奇门 → 美诺ESB的调用链路访问SAP
- 接口调用频率限制为每5分钟一次
- 必须遵循现有的数据库表结构和字段命名规范

### 业务约束
- 大家电退货必须等待运费补差SO单回传
- 平台强制退款需要特殊审核流程
- 小家电退货仅支持消费者自行寄回

### 假设
- SAP系统接口稳定可用
- 商品分类数据准确完整
- 邮件服务正常运行
- 网络连接稳定可靠

## 成功标准

### 完成定义
- [ ] 退货入库后能成功同步数据到SAP
- [ ] 大家电和小家电按不同逻辑正确处理
- [ ] 平台强制退款流程正常运行
- [ ] 邮件通知功能正常工作
- [ ] 数据库表管理方法正常运行
- [ ] 所有接口调用都有完整的日志记录

### 验收指标
- 数据同步成功率 ≥ 99%
- 接口响应时间 ≤ 30秒
- 邮件发送成功率 ≥ 95%
- 系统可用性 ≥ 99.5%

## 术语表

| 术语 | 定义 |
|------|------------|
| OMS | 订单管理系统(Order Management System) |
| SAP | 企业资源规划系统 |
| ESB | 企业服务总线(Enterprise Service Bus) |
| reship | 退货单据 |
| WO | 工单(Work Order) |
| EC | 电子商务部门 |
| LARGE_APPLIANCES | 大家电分类标识 |
| SMALL_APPLIANCES | 小家电分类标识 |
| reship_wo | 退货工单表 |
| inbound_status | 入库状态 |
| sap_sync_status | SAP同步状态 |
| flag_type | 退货单标识类型 |
| finder列表机制 | 框架提供的数据列表展示机制 |
| service注册 | 服务注册机制 |
| 补差订单 | 用于处理金额差异的订单 |
| 七天无理由退款 | 消费者权益保护的退货政策 |

---

## 需求审查检查清单

### 完整性
- [x] 所有用户故事都有明确的角色、功能和收益
- [x] 每个需求都有使用EARS格式的具体验收标准
- [x] 非功能性需求已得到解决
- [x] 成功标准已定义且可衡量

### 质量
- [x] 需求使用主动语态编写
- [x] 每个验收标准都是可测试的
- [x] 需求避免实现细节
- [x] 术语在整个文档中保持一致

### EARS格式验证
- [x] 当(WHEN)语句描述具体事件或触发器
- [x] 如果(IF)语句描述明确的条件或状态
- [x] 当...时(WHILE)语句描述持续行为
- [x] 在...处(WHERE)语句描述具体上下文
- [x] 所有语句使用"应当"表示系统响应

### 清晰度
- [x] 需求明确无歧义
- [x] 技术术语在术语表中有解释
- [x] 利益相关者能理解所有需求
- [x] 不存在冲突的需求

### 可追溯性
- [x] 需求已编号和组织
- [x] 需求之间的依赖关系明确
- [x] 需求与业务目标相关联
- [x] 假设和约束已记录

---

## 技术规范

### 接口调用链
```
OMS → 塔内ESB → 奇门 → 美诺ESB → SAP
```

### 接口详情

#### 1. SAP退货单创建接口
- **接口名称**: `miele.qimen.esb.reship.add`
- **调用频率**: 每5分钟轮询一次
- **触发条件**: inbound_status为已入库状态

#### 2. SAP回调接口
- **接口名称**: `miele.reship.addCallback`
- **用途**: 接收SAP返回的reship单号
- **回调时机**: SAP处理完成后主动回调

#### 3. 退货单创建回调接口
- **接口名称**: `miele.returnorder.addCallback`
- **用途**: 退货单创建回调处理

### Request Parameters Structure
```json
{
    "system": "",
    "header": {
        "otherSystemSO": "",
        "referenceSO": "",
        "orderType": "",
        "deliveryDate": "",
        "wmsOrder": "",
        "moveDate": ""
    },
    "item": [{
        "otherSystemItem": "",
        "referenceItem": "",
        "material": "",
        "unitPrice": "",
        "quantity": "",
        "plant": "",
        "shippingPoint": "",
        "storageLocation": "",
        "SN": [{
            "serialNumber": ""
        }]
    }]
}
```

### Response Structure
```json
{
    "rsp": "succ",
    "error_msg": null,
    "error_code": null,
    "data": {
        "message": "接口数据已保存至接口数据池",
        "status": "S"
    },
    "request_id": "687a21c4cc096",
    "res": ""
}
```

### 数据库表

#### 核心表
- `sdb_ome_reship`: 退货主表
- `sdb_ome_reship_items`: 退货商品明细表
- `reship_wo`: 退货工单表

#### 数据同步方法
```php
/**
 * 新增或更新reship_wo表数据
 * @param int $reship_id 退货单ID
 * @return bool 操作结果
 */
function updateReshipWo($reship_id) {
    // 根据reship_id获取sdb_ome_reship和sdb_ome_reship_items表数据
    // 按照字段映射关系新增或更新reship_wo表数据
}
```

### 业务规则

#### 1. 业务流程概述
1. 消费者在前台申请退货退款单，OMS接收平台待审核的售后申请单
2. 客服在电商平台审核退货退款单，OMS接收审核结果并自动生成退货单
3. 通过service注册将退换货单数据更新到reship_wo表中
4. 在miele应用中使用框架finder列表机制展示reship_wo数据
5. 只有inbound_status为已入库的才能推送给SAP

#### 2. 商品分类规则
- **大家电判断**: 只要有一个商品是大家电，则整单按大家电处理
- **订单分类值**:
  - `LARGE_APPLIANCES`: 专车类订单（大家电）
  - `SMALL_APPLIANCES`: 快递类订单（小家电）

#### 3. 七天无理由退款流程
**大家电流程:**
- 审核时选择退货原因和取件类型
- 取件类型：1（上门质检）、2（消费者自行寄回）、3（上门取件）
- 上门取件需填写取件地址、联系方式和物流费用
- 需要物流费用时生成补差订单（固定运费编码）
- 选择上门质检的需要质检操作，质检通过后填写相同信息

#### 4. 平台强制退款特殊处理
- **识别标识**: `sdb_ome_reship.flag_type & ome_reship_const::_QZ_REFUND_CODE`
- **自动处理**: 生成WO单时发送强制退款邮件给EC
- **默认设置**: 上门取件，无需质检
- **审核要求**: 需要填写取件地址
- **通知方式**: 客服线下邮件通知上门取件

#### 5. 补差订单规则
- **触发条件**: SAP同步成功且sap_sync_status=succ
- **人工判断**: 不自动判断补差，需要客服人工确认
- **验证规则**: 补差金额必须与行明细小计一致
- **生成规则**: 验证通过后生成补差SAP_SO单推送给SAP

#### 6. 界面展示规则
- **列表展示**: 根据同步状态展示标签页面
- **详情内容**: 包含商品明细和操作记录
- **操作按钮**: 审核和质检按钮（需先审核后质检）
- **补差按钮**: SAP同步成功后显示

---

## 约束与依赖

- **技术约束**: 必须遵循项目现有的 docs/cheatsheet 手册和架构。
- **外部依赖**: SAP系统接口稳定性，塔内ESB服务可用性。
