# 技术设计文档

---

## 文档信息

- **功能名称**: 退货对接SAP
- **版本**: 1.0
- **日期**: 2025-07-22
- **作者**: Kiro AI助手
- **审核人**: Miele开发团队
- **相关文档**: [需求文档](./requirements.md)

## 概述

基于对现有app/miele代码的深入分析，退货对接SAP功能需要在现有架构基础上进行重构和扩展。当前已存在部分退货处理逻辑，包括reship_wo表结构、service注册机制、finder展示等，但缺乏完整的SAP对接和业务流程管理。

本设计将重构现有代码，建立完整的退货业务流程管理系统，并实现与SAP系统的自动化数据同步。

### 设计目标
- 重构现有退货处理逻辑，建立标准化的业务流程
- 实现完整的SAP数据同步机制
- 提供用户友好的审核和质检界面
- 支持补差订单的自动化生成和管理

### 关键设计决策
- **重用现有架构**: 基于现有的service注册、finder机制和ESB服务架构
- **渐进式重构**: 保持现有接口兼容性，逐步增强功能
- **统一日志记录**: 使用现有的operation_log机制记录所有操作

## 系统架构

### 系统上下文

```mermaid
graph TB
    A[电商平台] --> B[OMS系统]
    B --> C[Miele应用]
    C --> D[塔内ESB]
    D --> E[奇门]
    E --> F[美诺ESB]
    F --> G[SAP系统]

    H[客服人员] --> C
    I[质检人员] --> C
```

### 高层架构

```mermaid
graph LR
    A[服务层] --> B[控制器层]
    B --> C[查找器层]
    C --> D[模型层]
    A --> E[ESB接口层]
    E --> F[定时任务层]

    G[开放API层] --> A
```

### 技术栈
| 层级 | 技术 | 选择理由 |
|-------|------------|-----------|
| 前端 | Smarty模板 + JavaScript | 遵循现有框架标准 |
| 后端 | PHP + ShopEx框架 | 现有技术栈 |
| 数据库 | MySQL | 现有数据库 |
| 集成 | ESB + 奇门接口 | 现有集成架构 |

## 组件和接口

### 组件1: 退货服务层重构

**目的**: 处理退货单创建、审核、质检等业务逻辑

**现有代码分析**:
- `miele_service_reship_create`: 已实现基础退货单创建逻辑
- `miele_service_reship_check`: 已实现基础质检验证逻辑
- 需要扩展：审核流程、补差生成、SAP同步触发

**重构计划**:
```php
// 扩展现有service类
class miele_service_reship_create {
    // 现有方法保持兼容
    public function after_create($reshipId) { /* 现有逻辑 */ }

    // 新增方法
    public function audit_reship($reshipId, $auditData) { /* 审核逻辑 */ }
    public function trigger_sap_sync($reshipId) { /* SAP同步触发 */ }
}
```

**职责**:
- 退货单数据同步到reship_wo表
- 审核流程处理和状态更新
- 质检流程管理
- 补差订单生成触发
- SAP同步状态管理

### 组件2: ESB接口层扩展

**目的**: 实现退货相关的SAP接口调用

**现有代码分析**:
- ESB基础架构已存在：`miele_esb_service`基类
- 现有ESB实现：`miele_esb_syncso`, `miele_esb_syncdebitnote`
- 缺失：退货专用的ESB接口实现

**新增实现**:
```php
class miele_esb_syncreship extends miele_esb_service {
    private $method = 'miele.qimen.esb.reship.add';

    public function pushReship($reshipWoId) {
        // 实现退货单推送逻辑
    }
}

class miele_esb_callback_reship {
    public function addCallback($params) {
        // 实现SAP回调处理
    }
}
```

**接口**:
- **输入**: reship_wo表数据
- **输出**: SAP接口调用结果
- **依赖**: 塔内ESB服务

### 组件3: 控制器层增强

**目的**: 提供退货管理的Web界面

**现有代码分析**:
- `miele_controller_admin_reship`: 已有基础框架
- 现有功能：audit(), quality_check()方法
- 需要增强：补差处理、批量操作、状态管理

**增强计划**:
```php
class miele_controller_admin_reship extends desktop_controller {
    // 现有方法保持兼容
    public function audit() { /* 现有审核逻辑 */ }
    public function quality_check() { /* 现有质检逻辑 */ }

    // 新增方法
    public function bucha() { /* 补差处理 */ }
    public function batch_sync() { /* 批量SAP同步 */ }
    public function sync_status() { /* 同步状态查看 */ }
}
```

### 组件4: 定时任务层

**目的**: 自动化SAP数据同步

**参考现有实现**: `miele_autotask_timer_pushbucha`

**新增实现**:
```php
class miele_autotask_timer_pushreship {
    public function process($params, &$err_msg) {
        // 查询待同步的退货单
        // 调用ESB接口推送
        // 更新同步状态
    }
}
```

## 数据模型

### 现有表结构分析

**reship_wo表** (已存在，需要扩展):
```sql
-- 现有字段保持不变
-- 需要新增的字段：
ALTER TABLE miele_reship_wo ADD COLUMN sap_sync_status VARCHAR(20) DEFAULT 'pending';
ALTER TABLE miele_reship_wo ADD COLUMN sap_sync_time DATETIME NULL;
ALTER TABLE miele_reship_wo ADD COLUMN sap_reship_bn VARCHAR(50) DEFAULT '';
ALTER TABLE miele_reship_wo ADD COLUMN bucha_status VARCHAR(20) DEFAULT 'pending';
```

**reship_wo_items表** (已存在，结构完整):
- 现有结构满足需求，无需修改

### 配置管理

**退货配置常量** (使用常量类，不新增配置表):
```php
class miele_service_reship_config {
    const LOGISTICS_FEE_MATERIAL_CODE = 'FREIGHT001';
    const SYNC_BATCH_SIZE = 50;
    // 其他配置常量...
}
```

## API设计

### 现有开放API扩展

**分析现有配置**: `miele_openapi_conf`已有基础结构

**新增接口配置**:
```php
// 在miele_openapi_conf中新增
'miele.reship' => array(
    'label' => '退货单',
    'methods' => array(
        'addCallback' => '退货单创建回调',
        'syncCallback' => '退货单同步回调',
    ),
    'group' => 'miele',
),
```

### 接口实现

**退货回调接口**:
```php
class miele_openapi_function_v1_reship extends openapi_api_function_abstract {
    public function addCallback($params, &$code, &$sub_msg) {
        return kernel::single('miele_esb_callback_reship')->addCallback($params);
    }
}
```

## 安全考虑

### 现有安全机制
- 基于现有的权限检查机制
- 使用operation_log记录所有操作
- ESB接口的安全认证

### 新增安全措施
- 审核操作的权限控制
- 补差金额的验证和限制
- SAP接口调用的安全日志

## 错误处理

### 统一错误处理策略
- 使用现有的operation_log机制记录错误，记录简洁的错误描述
- ESB接口调用失败的重试机制
- 用户友好的错误提示

### 操作日志记录规范
- 使用简洁的中文描述，避免记录大量数据
- 记录关键业务信息：订单号、状态、金额等
- 避免使用json_encode记录完整数组数据
- 示例格式：
  - 审核：`退货审核，7天无理由：是，取件类型：上门质检`
  - 质检：`退货质检，结果：通过`
  - 同步：`退货单同步成功，退货单号：R202501220001`
  - 补差：`补差处理，金额：50.00元`

### 错误分类
| 错误类型 | 处理方式 | 用户提示 |
|---------|---------|---------|
| 数据验证错误 | 前端验证+后端验证 | 具体字段错误信息 |
| SAP接口错误 | 记录日志+重试 | "系统繁忙，请稍后重试" |
| 业务规则错误 | 业务验证 | 具体业务规则说明 |

## 性能考虑

### 现有性能优化
- 使用现有的finder机制进行数据分页
- 数据库索引优化
- 定时任务的锁机制防止重复执行

### 新增优化措施
- SAP接口调用的批量处理
- 缓存常用配置数据
- 异步处理大批量数据同步

## 测试策略

### 单元测试
- 服务层业务逻辑测试
- ESB接口调用测试
- 数据验证逻辑测试

### 集成测试
- SAP接口端到端测试
- 数据库事务测试
- 定时任务执行测试

### 用户验收测试
- 审核流程完整性测试
- 质检流程测试
- 补差功能测试

## 迁移和兼容性

### 数据迁移策略
- 现有reship_wo数据保持不变
- 新增字段使用默认值
- 历史数据的状态初始化

### 向后兼容性
- 现有service接口保持兼容
- 现有finder展示逻辑保持不变
- 新增功能通过配置开关控制

---

## 参考资料

- **参考的速查手册**:
  - docs/cheatsheet/database/schema-config.md
  - docs/cheatsheet/openapi-template.md
  - docs/cheatsheet/system/menu-service-config.md
- **参考的参考文档**:
  - docs/reference/miele-bucha-system-reference.md
  - docs/reference/miele-odn-interface-reference.md
- **参考的现有代码**: app/miele目录下的service、controller、finder等实现

## 产出评估

- **是否需要生成新的速查手册?**: 是
  - **理由**: 退货SAP对接的设计模式具有复用性，可为其他类似集成提供参考
  - **计划**: 创建"退货SAP集成开发手册"

- **是否需要生成参考文档?**: 是
  - **理由**: 退货对接SAP的详细技术实现需要作为长期参考文档

---

## 详细实现方案

### 1. 现有代码重构分析

#### 服务层重构重点

**miele_service_reship_create类扩展**:
```php
<?php
class miele_service_reship_create {
    // 保持现有after_create方法兼容性
    public function after_create($reshipId) {
        // 现有逻辑：创建reship_wo记录
        // 新增：初始化SAP同步状态
        $result = $this->_createReshipWo($reshipId);
        if ($result[0]) {
            $this->_initSapSyncStatus($reshipId);
        }
        return $result;
    }

    // 新增：审核处理方法
    public function process_audit($reshipWoId, $auditData) {
        $reshipWoMdl = app::get('miele')->model('reship_wo');
        $reshipWo = $reshipWoMdl->db_dump(['id' => $reshipWoId]);

        if (empty($reshipWo)) {
            return array(false, array('msg' => '退货工单不存在'));
        }

        // 判断是否为平台强制退款
        $isForcedRefund = ($reshipWo['flag_type'] & ome_reship_const::_QZ_REFUND_CODE) > 0;

        $updateData = [
            'is_7toreturn' => $auditData['is_7toreturn'],
        ];

        if ($isForcedRefund) {
            // 平台强制退款：默认上门取件，无需质检
            $updateData['pickup_type'] = '3'; // 上门取件
            $updateData['qc_result'] = 'pass'; // 无需质检，直接通过
            $updateData['contact_name'] = $auditData['contact_name'];
            $updateData['contact_phone'] = $auditData['contact_phone'];
            $updateData['contact_address'] = $auditData['contact_address'];
        } else {
            // 普通退货流程
            $updateData['pickup_type'] = $auditData['pickup_type'];

            if ($auditData['pickup_type'] == '3') { // 上门取件
                $updateData['contact_name'] = $auditData['contact_name'];
                $updateData['contact_phone'] = $auditData['contact_phone'];
                $updateData['contact_address'] = $auditData['contact_address'];
                $updateData['logistics_fee_required'] = $auditData['logistics_fee_required'];
                if ($auditData['logistics_fee_required'] == '1') {
                    $updateData['logistics_fee'] = $auditData['logistics_fee'];
                }
            }
        }

        $reshipWoMdl->update($updateData, ['id' => $reshipWoId]);

        // 生成补差订单（如需要）
        if (isset($auditData['logistics_fee_required']) && $auditData['logistics_fee_required'] == '1') {
            $this->_generateLogisticsFeeOrder($reshipWoId, $auditData['logistics_fee']);
        }

        // 记录操作日志
        $opObj = app::get('ome')->model('operation_log');
        $opObj->write_log('reship_audit@miele', $reshipWoId,
            sprintf('退货审核，7天无理由：%s，取件类型：%s',
                $auditData['is_7toreturn'],
                $updateData['pickup_type']
            )
        );

        return array(true, array('msg' => '审核成功'));
    }

    // 新增：质检处理方法
    public function process_quality_check($reshipWoId, $qcData) {
        $reshipWoMdl = app::get('miele')->model('reship_wo');

        $updateData = [
            'qc_result' => $qcData['qc_result'],
        ];

        if ($qcData['qc_result'] == 'pass') {
            // 质检通过，需要填写联系方式和物流费用
            $updateData['contact_name'] = $qcData['contact_name'];
            $updateData['contact_phone'] = $qcData['contact_phone'];
            $updateData['contact_address'] = $qcData['contact_address'];
            $updateData['logistics_fee_required'] = $qcData['logistics_fee_required'];
            if ($qcData['logistics_fee_required'] == '1') {
                $updateData['logistics_fee'] = $qcData['logistics_fee'];
            }
        }

        $reshipWoMdl->update($updateData, ['id' => $reshipWoId]);

        // 生成补差订单（如需要）
        if ($qcData['qc_result'] == 'pass' && isset($qcData['logistics_fee_required']) && $qcData['logistics_fee_required'] == '1') {
            $this->_generateLogisticsFeeOrder($reshipWoId, $qcData['logistics_fee']);
        }

        // 记录操作日志
        $opObj = app::get('ome')->model('operation_log');
        $opObj->write_log('reship_qc@miele', $reshipWoId,
            sprintf('退货质检，结果：%s', $qcData['qc_result'])
        );

        return array(true, array('msg' => '质检成功'));
    }

    // 新增：生成物流费用补差订单
    private function _generateLogisticsFeeOrder($reshipWoId, $logisticsFee) {
        // 生成补差订单逻辑
        // 物料编码为固定的运费编码
        $logisticsMaterialCode = miele_service_reship_config::LOGISTICS_FEE_MATERIAL_CODE;

        // 调用补差订单生成服务
        return kernel::single('miele_service_bucha_so')->generateLogisticsFeeOrder($reshipWoId, $logisticsFee, $logisticsMaterialCode);
    }
}
```

**miele_service_reship_check类扩展**:
```php
<?php
class miele_service_reship_check {
    // 保持现有方法
    public function check_valid($reshipId) { /* 现有逻辑 */ }
    public function after_check($reshipId) { /* 现有逻辑 */ }

    // 新增：SAP同步前检查
    public function check_sap_sync_ready($reshipWoId) {
        // 检查入库状态
        // 检查审核状态
        // 检查质检状态（如需要）
        // 检查补差状态
    }
}
```

#### 查找器层增强

**miele_finder_reship类扩展**:
```php
<?php
class miele_finder_reship {
    // 保持现有column_edit方法
    public function column_edit($row) {
        $btn = [];

        // 现有按钮逻辑
        if($row['is_7toreturn'] == 'pending') {
            $btn[] = '审核按钮';
        }
        if($row['seven_day_return'] == 'yes' && $row['pickup_type'] == '1' && $row['qc_status'] == '0') {
            $btn[] = '质检按钮';
        }

        // 新增按钮
        if($row['sap_sync_status'] == 'succ') {
            $btn[] = '<a class="lnk" target="dialog::{width:800,height:600,title:\'补差处理\'}" href="index.php?app=miele&ctl=admin_reship&act=bucha&id='.$row['id'].'">补差</a>';
        }

        return implode('|', $btn);
    }

    // 新增：SAP同步状态显示
    public function column_sap_sync_status($row) {
        $statusMap = [
            'pending' => '<span class="label label-warning">待同步</span>',
            'running' => '<span class="label label-info">同步中</span>',
            'succ' => '<span class="label label-success">同步成功</span>',
            'fail' => '<span class="label label-danger">同步失败</span>',
        ];
        return $statusMap[$row['sap_sync_status']] ?? $row['sap_sync_status'];
    }

    // 修改：操作按钮逻辑完善
    public function column_edit($row) {
        $btn = [];

        // 审核按钮 - 7天无理由待确认状态
        if($row['is_7toreturn'] == 'pending') {
            $btn[] = '<a class="lnk" target="dialog::{width:800,height:600,title:\'退货审核\'}" href="index.php?app=miele&ctl=admin_reship&act=audit&id='.$row['id'].'">审核</a>';
        }

        // 质检按钮 - 审核完成且选择上门质检
        if($row['is_7toreturn'] == 'yes' && $row['pickup_type'] == '1' && $row['qc_result'] == 'pending') {
            $btn[] = '<a class="lnk" target="dialog::{width:800,height:600,title:\'质检操作\'}" href="index.php?app=miele&ctl=admin_reship&act=quality_check&id='.$row['id'].'">质检</a>';
        }

        // 补差按钮 - SAP同步成功后显示
        if($row['sap_sync_status'] == 'succ') {
            $btn[] = '<a class="lnk" target="dialog::{width:800,height:600,title:\'补差处理\'}" href="index.php?app=miele&ctl=admin_reship&act=bucha&id='.$row['id'].'">补差</a>';
        }

        return implode('|', $btn);
    }
}
```

### 2. 新增组件实现

#### ESB接口实现

**退货SAP同步接口**:
```php
<?php
class miele_esb_syncreship extends miele_esb_service {
    private $method = 'miele.qimen.esb.reship.add';

    public function pushReship($reshipWoId) {
        $reshipWoMdl = app::get('miele')->model('reship_wo');
        $reshipWo = $reshipWoMdl->db_dump(['id' => $reshipWoId, 'sap_sync_status' => ['pending','fail']], '*');

        if (empty($reshipWo)) {
            return array('res' => 'fail', 'message' => '退货工单不存在或状态不允许同步');
        }

        // 检查入库状态
        if ($reshipWo['inbound_status'] != 'inbound') {
            return array('res' => 'fail', 'message' => '退货单未入库，不能同步SAP');
        }

        try {
            $reshipData = $this->_formatReshipData($reshipWo);
        } catch (Exception $e) {
            return array('res' => 'fail', 'message' => $e->getMessage());
        }

        $this->original_bn = $reshipWo['reship_bn'];
        $res = $this->qimenRequest($this->method, $reshipData);

        // 记录操作日志
        $opObj = app::get('ome')->model('operation_log');
        $opObj->write_log(
            'reship_sync@miele',
            $reshipWo['id'],
            sprintf('退货单同步%s，退货单号：%s',
                ($res['rsp'] == 'succ' ? '成功' : '失败'),
                $reshipWo['reship_bn']
            )
        );

        if ($res['rsp'] == 'succ') {
            $updateData = [
                'sap_sync_status' => 'running',
                'sap_sync_time' => time(),
            ];
            $reshipWoMdl->update($updateData, ['id'=>$reshipWo['id']]);
            return array('res' => 'succ', 'message' => '退货单推送成功');
        } else {
            return array('res' => 'fail', 'message' => $res['msg'] ?? '退货单推送失败');
        }
    }

    private function _formatReshipData($reshipWo) {
        // 格式化退货数据为SAP接口格式
        $itemsMdl = app::get('miele')->model('reship_wo_items');
        $items = $itemsMdl->getList('*', ['reship_wo_id' => $reshipWo['id']]);

        $sapData = [
            'system' => 'OMS',
            'header' => [
                'otherSystemSO' => $reshipWo['reship_bn'],
                'referenceSO' => $reshipWo['order_bn'],
                'orderType' => $reshipWo['is_major_appliance'] ? 'LARGE_APPLIANCES' : 'SMALL_APPLIANCES',
                'deliveryDate' => date('Y-m-d', $reshipWo['reship_apply_time']),
                'wmsOrder' => $reshipWo['reship_bn'],
                'moveDate' => date('Y-m-d'),
            ],
            'item' => []
        ];

        foreach ($items as $item) {
            $sapData['item'][] = [
                'otherSystemItem' => $item['oid'],
                'referenceItem' => $item['order_item_id'],
                'material' => $item['bn'],
                'unitPrice' => $item['apply_amount'],
                'quantity' => $item['apply_num'],
                'plant' => '', // 需要根据业务规则填充
                'shippingPoint' => '', // 需要根据业务规则填充
                'storageLocation' => '', // 需要根据业务规则填充
                'SN' => [] // 序列号信息
            ];
        }

        return $sapData;
    }
}
```

**SAP回调处理**:
```php
<?php
class miele_esb_callback_reship {
    public function addCallback($params) {
        try {
            $reshipWoMdl = app::get('miele')->model('reship_wo');

            // 根据退货单号查找记录
            $reshipWo = $reshipWoMdl->db_dump(['reship_bn' => $params['otherSystemSO']], '*');
            if (empty($reshipWo)) {
                return array('rsp' => 'fail', 'msg' => '退货工单不存在');
            }

            // 更新SAP同步状态
            $updateData = [
                'sap_sync_status' => 'succ',
                'sap_reship_bn' => $params['referenceSO'] ?? '',
                'sap_sync_time' => time(),
            ];

            $reshipWoMdl->update($updateData, ['id' => $reshipWo['id']]);

            // 记录操作日志
            $opObj = app::get('ome')->model('operation_log');
            $opObj->write_log(
                'reship_callback@miele',
                $reshipWo['id'],
                sprintf('SAP退货单回调成功，退货单号：%s',
                    $params['otherSystemSO']
                )
            );

            return array('rsp' => 'succ', 'msg' => '回调处理成功');

        } catch (Exception $e) {
            kernel::log('退货单回调处理失败: ' . $e->getMessage());
            return array('rsp' => 'fail', 'msg' => '回调处理失败: ' . $e->getMessage());
        }
    }
}
```

#### 定时任务实现

**文件路径**: `app/miele/lib/autotask/timer/pushreship.php` (根据class-loading-rules.md)

**退货单自动同步任务** (参考现有定时任务实现):
```php
<?php
/**
 * 退货单SAP自动同步定时任务
 * 类名: miele_autotask_timer_pushreship
 * 文件: app/miele/lib/autotask/timer/pushreship.php
 */
class miele_autotask_timer_pushreship {

    /**
     * 任务锁key
     */
    const TASK_LOCK_KEY = 'miele_pushreship_timer_lock';

    /**
     * 锁超时时间(秒)
     */
    const LOCK_TIMEOUT = 300;

    /**
     * 任务执行入口
     * @param array $params 任务参数
     * @param string $error_msg 错误信息
     * @return bool 执行结果
     */
    public function process($params, &$error_msg = '') {
        try {
            // 检查任务锁
            if (!$this->acquireLock()) {
                $error_msg = '退货单同步任务正在执行中，跳过本次执行';
                return true; // 返回true避免被认为是错误
            }

            $reshipWoMdl = app::get('miele')->model('reship_wo');

            // 查询待同步的退货单
            $filter = [
                'sap_sync_status' => 'pending',
                'inbound_status' => '1', // 已入库
                'cs_status' => '5', // 客服处理完成
            ];

            $reshipWos = $reshipWoMdl->getList('id', $filter, 0, 50); // 每次处理50条

            $successCount = 0;
            $failCount = 0;

            foreach ($reshipWos as $reshipWo) {
                $result = kernel::single('miele_esb_syncreship')->pushReship($reshipWo['id']);
                if ($result['res'] == 'succ') {
                    $successCount++;
                } else {
                    $failCount++;
                    kernel::log('退货单同步失败: ID=' . $reshipWo['id'] . ', 错误=' . $result['message']);
                }
            }

            $error_msg = sprintf('退货单自动同步完成: 成功=%d, 失败=%d', $successCount, $failCount);
            kernel::log($error_msg);

            // 释放任务锁
            $this->releaseLock();
            return true;

        } catch (Exception $e) {
            // 释放任务锁
            $this->releaseLock();

            $error_msg = '退货单自动同步异常: ' . $e->getMessage();
            kernel::log($error_msg);
            return false;
        }
    }

    /**
     * 获取任务锁
     */
    private function acquireLock() {
        $lockValue = uniqid();
        $result = base_kvstore::instance('miele')->store(self::TASK_LOCK_KEY, $lockValue, self::LOCK_TIMEOUT);
        if ($result) {
            $this->lockValue = $lockValue;
            return true;
        }
        return false;
    }

    /**
     * 释放任务锁
     */
    private function releaseLock() {
        if (isset($this->lockValue)) {
            base_kvstore::instance('miele')->delete(self::TASK_LOCK_KEY);
            unset($this->lockValue);
        }
    }
}
```

**定时任务配置** (在app/taskmgr/lib/whitelist/miele.php中添加):
```php
public static function timer_list() {
    $_tasks = array(
        // 现有任务保持不变
        'pushbucha' => array(
            'method' => 'miele_autotask_timer_pushbucha',
            'threadNum' => 1,
            'timeout' => 300,
            'name' => '补差单推送任务',
        ),
        // 新增退货单同步任务
        'pushreship' => array(
            'method' => 'miele_autotask_timer_pushreship',
            'threadNum' => 1,
            'timeout' => 300,
            'name' => '退货单SAP同步任务',
        ),
    );
    return $_tasks;
}

public static function init_list() {
    $_tasks = array(
        // 现有任务保持不变
        'pushbuchadomainqueue' => array(
            'threadNum' => 1,
            'rule' => '0 */5 * * * *', // 每5分钟执行
        ),
        // 新增退货单同步任务
        'pushreshipdomainqueue' => array(
            'threadNum' => 1,
            'rule' => '0 */5 * * * *', // 每5分钟执行一次
        ),
    );
    return $_tasks;
}
```

### 3. 数据库表结构分析

**现有reship_wo表分析** (参考schema-config.md):
根据现有的`app/miele/dbschema/reship_wo.php`，表结构已经包含了大部分需要的字段：

✅ **已存在的关键字段**:
- `sap_sync_status`: SAP同步状态 (none/pending/running/succ/fail)
- `sap_sync_msg`: SAP同步信息
- `sap_reship_bn`: SAP退货单号
- `adj_order_bn`: 补差SO单号
- `is_7toreturn`: 7天无理由 (yes/no/pending)
- `cs_status`: 客服介入状态 (0-11)
- `pickup_type`: 取件类型 (1-3)
- `logistics_fee_required`: 需支付物流费用 (0/1)
- `logistics_fee`: 物流费用金额
- `qc_result`: 质检结果 (pending/pass/fail)
- `inbound_status`: 入库状态 (0-3)

**需要新增的字段** (严格按照schema-config.md规范):
```php
// 在app/miele/dbschema/reship_wo.php的columns数组中新增
'sap_sync_time' => array(
    'type' => 'time',
    'label' => 'SAP同步时间',
    'editable' => false,
    'in_list' => true,
    'default_in_list' => false,
    'width' => 140,
    'order' => 285,
    'comment' => 'SAP同步时间',
),
'bucha_status' => array(
    'type' => array(
        'pending' => '待处理',
        'processing' => '处理中',
        'completed' => '已完成',
    ),
    'label' => '补差状态',
    'default' => 'pending',
    'editable' => false,
    'width' => 80,
    'in_list' => true,
    'default_in_list' => true,
    'order' => 350,
    'filtertype' => 'normal',
    'filterdefault' => true,
    'comment' => '补差状态：pending(待处理),processing(处理中),completed(已完成)',
),
```

**注意**: `is_7toreturn`字段已存在，无需新增。现有定义：
```php
'is_7toreturn' => array(
    'type' => array(
        'yes' => '是',
        'no' => '否',
        'pending' => '待确认'
    ),
    'label' => '7天无理由',
    'default' => 'pending',
    // ... 其他属性
),
```

**需要新增的索引** (在index数组中新增):
```php
'idx_sap_sync_time' => array('columns' => array('sap_sync_time')),
'idx_bucha_status' => array('columns' => array('bucha_status')),
// 注意：is_7toreturn的索引已存在：'idx_is_7toreturn'
// 注意：sap_sync_status和inbound_status的索引可能已存在，需要检查
```

**字段属性说明** (遵循schema-config.md规范):
- `type`: 字段类型，枚举状态使用数组形式
- `label`: 显示标签
- `default`: 默认值
- `editable`: 是否可编辑，设为false
- `width`: 列宽度，状态字段80-100，时间字段140
- `in_list`: 是否在列表中显示
- `default_in_list`: 是否默认显示
- `order`: 显示顺序
- `filtertype`: 筛选类型，normal表示普通筛选
- `filterdefault`: 是否默认显示在筛选器
- `comment`: 字段注释，说明可选值

**实际需要新增的字段总结**:
仅需新增2个字段：
1. `sap_sync_time`: SAP同步时间
2. `bucha_status`: 补差状态

其他业务需要的字段如`is_7toreturn`、`sap_sync_status`等已在现有表结构中存在。

**不创建新配置表的原因**:
根据要求不能新增配置表，配置信息将通过以下方式处理：
1. 硬编码在代码中的常量
2. 使用现有的系统配置机制
3. 在service类中定义配置数组

### 4. Controller层详细实现

#### 补差处理Controller

**文件路径**: `app/miele/controller/admin/reship.php` (扩展现有文件)

```php
<?php
/**
 * 退货管理控制器
 * 类名: miele_ctl_admin_reship
 * 文件: app/miele/controller/admin/reship.php
 */
class miele_ctl_admin_reship extends desktop_controller {

    /**
     * 退货工单列表页面 (扩展现有index方法)
     */
    public function index() {
        $params = array(
            'use_view_tab' => true,  // 启用Tab功能
        );
        $this->finder('miele_mdl_reship_wo', $params);
    }

    /**
     * 退货审核页面
     */
    public function audit() {
        $this->begin('index.php?app=miele&ctl=admin_reship&act=index');

        $id = $_GET['id'];
        $model = app::get('miele')->model('reship_wo');

        if ($_POST) {
            $data = $_POST;

            try {
                // 处理审核逻辑
                $result = kernel::single('miele_service_reship_create')->process_audit($id, $data);
                if (!$result[0]) {
                    $this->end(false, $result[1]['msg']);
                }

                $this->end(true, '审核完成');

            } catch (Exception $e) {
                $this->end(false, '审核失败：' . $e->getMessage());
            }
        }

        // 获取退货工单数据
        $reshipWo = $model->db_dump(['id' => $id]);
        $this->pagedata['data'] = $reshipWo;

        // 判断商品类型（大家电/小家电）
        $itemsModel = app::get('miele')->model('reship_wo_items');
        $items = $itemsModel->getList('*', ['reship_wo_id' => $id]);
        $isLargeAppliance = $this->_checkIsLargeAppliance($items);
        $this->pagedata['is_large_appliance'] = $isLargeAppliance;

        // 判断是否为平台强制退款
        $isForcedRefund = ($reshipWo['flag_type'] & ome_reship_const::_QZ_REFUND_CODE) > 0;
        $this->pagedata['is_forced_refund'] = $isForcedRefund;

        $this->display('admin/reship/audit.html');
    }

    /**
     * 质检操作页面
     */
    public function quality_check() {
        $this->begin('index.php?app=miele&ctl=admin_reship&act=index');

        $id = $_GET['id'];
        $model = app::get('miele')->model('reship_wo');

        if ($_POST) {
            $data = $_POST;

            try {
                // 处理质检逻辑
                $result = kernel::single('miele_service_reship_create')->process_quality_check($id, $data);
                if (!$result[0]) {
                    $this->end(false, $result[1]['msg']);
                }

                $this->end(true, '质检完成');

            } catch (Exception $e) {
                $this->end(false, '质检失败：' . $e->getMessage());
            }
        }

        // 获取退货工单数据
        $reshipWo = $model->db_dump(['id' => $id]);
        $this->pagedata['data'] = $reshipWo;

        $this->display('admin/reship/quality_check.html');
    }

    /**
     * 检查是否为大家电
     */
    private function _checkIsLargeAppliance($items) {
        foreach ($items as $item) {
            // 根据商品信息判断是否为大家电
            // 只要有一个商品是大家电，则整单按大家电处理
            if ($this->_isLargeApplianceProduct($item['bn'])) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断商品是否为大家电
     */
    private function _isLargeApplianceProduct($productBn) {
        // 这里需要根据实际的商品分类逻辑来判断
        // 可能需要查询商品主数据或根据商品编码规则判断
        return false; // 临时返回，需要根据实际业务逻辑实现
    }

    /**
     * Tab标签页配置 (新增方法，参考tab-features.md)
     */
    public function _views() {
        $reshipWoMdl = app::get('miele')->model('reship_wo');

        $sub_menu = array(
            0 => array('label' => '全部', 'filter' => array()),
            1 => array('label' => '待审核', 'filter' => array('is_7toreturn' => 'pending')),
            2 => array('label' => '待质检', 'filter' => array(
                'seven_day_return' => 'yes',
                'pickup_type' => '1',
                'qc_status' => '0'
            )),
            3 => array('label' => '待同步', 'filter' => array(
                'sap_sync_status' => 'pending',
                'inbound_status' => 'inbound'
            )),
            4 => array('label' => '同步中', 'filter' => array('sap_sync_status' => 'running')),
            5 => array('label' => '同步成功', 'filter' => array('sap_sync_status' => 'succ')),
            6 => array('label' => '同步失败', 'filter' => array('sap_sync_status' => 'fail')),
        );

        foreach ($sub_menu as $k => $v) {
            $sub_menu[$k]['filter'] = $v['filter'] ? $v['filter'] : null;
            $sub_menu[$k]['addon'] = $reshipWoMdl->count($v['filter']);
            $sub_menu[$k]['href'] = "index.php?app=miele&ctl=admin_reship&act=index&view=" . $k;
            $sub_menu[$k]['optional'] = false;
        }

        return $sub_menu;
    }

    // 新增：补差处理页面
    public function bucha() {
        $this->begin('index.php?app=miele&ctl=admin_reship&act=index');

        $id = $_GET['id'];
        $model = app::get('miele')->model('reship_wo');

        if ($_POST) {
            $data = $_POST;

            // 验证补差数据
            $validation = $this->_validateBuchaData($data);
            if (!$validation['valid']) {
                $this->end(false, $validation['message']);
            }

            try {
                // 生成补差SO单
                $result = kernel::single('miele_service_bucha_so')->generateBuchaOrder($id, $data);
                if (!$result[0]) {
                    $this->end(false, $result[1]['msg']);
                }

                // 更新补差状态
                $model->update(['bucha_status' => 'processing'], ['id' => $id]);

                // 记录操作日志
                $opObj = app::get('ome')->model('operation_log');
                $opObj->write_log('reship_bucha@miele', $id, sprintf('补差处理，金额：%s元', $data['bucha_amount']));

                $this->end(true, '补差订单生成成功');

            } catch (Exception $e) {
                $this->end(false, '补差处理失败：' . $e->getMessage());
            }
        }

        // 获取退货工单数据
        $reshipWo = $model->db_dump(['id' => $id]);
        $this->pagedata['data'] = $reshipWo;

        // 获取订单明细
        $itemsModel = app::get('miele')->model('reship_wo_items');
        $items = $itemsModel->getList('*', ['reship_wo_id' => $id]);
        $this->pagedata['items'] = $items;

        $this->display('admin/reship/bucha.html');
    }

    // 批量SAP同步
    public function batch_sync() {
        $this->begin('index.php?app=miele&ctl=admin_reship&act=index');

        $ids = $_POST['ids'] ?? [];
        if (empty($ids)) {
            $this->end(false, '请选择要同步的记录');
        }

        $successCount = 0;
        $failCount = 0;
        $errors = [];

        foreach ($ids as $id) {
            $result = kernel::single('miele_esb_syncreship')->pushReship($id);
            if ($result['res'] == 'succ') {
                $successCount++;
            } else {
                $failCount++;
                $errors[] = "ID {$id}: " . $result['message'];
            }
        }

        $message = "同步完成：成功 {$successCount} 条，失败 {$failCount} 条";
        if (!empty($errors)) {
            $message .= "\n失败详情：\n" . implode("\n", $errors);
        }

        $this->end($failCount == 0, $message);
    }

    private function _validateBuchaData($data) {
        // 验证补差金额
        if (!isset($data['bucha_amount']) || !is_numeric($data['bucha_amount'])) {
            return ['valid' => false, 'message' => '补差金额必须是数字'];
        }

        if ($data['bucha_amount'] <= 0) {
            return ['valid' => false, 'message' => '补差金额必须大于0'];
        }

        // 验证明细数据
        if (empty($data['items']) || !is_array($data['items'])) {
            return ['valid' => false, 'message' => '缺少补差明细数据'];
        }

        // 计算明细小计
        $totalAmount = 0;
        foreach ($data['items'] as $item) {
            if (isset($item['selected']) && $item['selected'] == '1') {
                $totalAmount += floatval($item['bucha_amount'] ?? 0);
            }
        }

        // 验证金额一致性
        if (abs($totalAmount - floatval($data['bucha_amount'])) > 0.01) {
            return ['valid' => false, 'message' => '补差金额与明细小计不一致'];
        }

        return ['valid' => true];
    }
}
```

### 5. 服务注册配置

#### services.xml扩展

**文件路径**: `app/miele/services.xml` (扩展现有文件)

```xml
<services>
    <!-- 现有服务保持不变 -->
    <service id="operation_log">
        <class>miele_operation_log</class>
    </service>
    <!-- ... 其他现有服务 -->

    <!-- 新增退货相关服务 -->
    <!-- 注意：根据class-loading-rules.md，业务逻辑类无需注册，自动加载 -->
    <!-- 只有Finder类需要注册 -->

    <!-- 现有Finder服务保持不变 -->
    <service id="desktop_finder.miele_mdl_reship_wo">
        <class>miele_finder_reship</class>
    </service>

    <!-- 其他服务通过kernel::single()自动加载，无需注册 -->
    <!-- 例如：kernel::single('miele_esb_syncreship') -->
    <!-- 例如：kernel::single('miele_service_bucha_so') -->
    <!-- 例如：kernel::single('miele_autotask_timer_pushreship') -->
</services>
```

#### 文件创建清单 (根据class-loading-rules.md)

**需要创建的新文件**:
```
app/miele/lib/esb/syncreship.php              # miele_esb_syncreship
app/miele/lib/esb/callback/reship.php         # miele_esb_callback_reship
app/miele/lib/service/bucha/so.php            # miele_service_bucha_so
app/miele/lib/service/reship/config.php       # miele_service_reship_config
app/miele/lib/autotask/timer/pushreship.php   # miele_autotask_timer_pushreship
app/miele/lib/openapi/params/v1/reship.php    # miele_openapi_params_v1_reship
app/miele/lib/openapi/function/v1/reship.php  # miele_openapi_function_v1_reship
app/miele/view/admin/reship/audit.html         # 审核页面模板
app/miele/view/admin/reship/quality_check.html # 质检页面模板
app/miele/view/admin/reship/bucha.html         # 补差处理模板
```

**需要修改的现有文件**:
```
app/miele/controller/admin/reship.php         # 扩展现有控制器（新增audit、quality_check方法）
app/miele/lib/finder/reship.php               # 扩展现有Finder（完善操作按钮逻辑）
app/miele/lib/openapi/conf.php                # 扩展OpenAPI配置
app/miele/lib/service/reship/create.php       # 扩展现有Service（新增审核、质检处理方法）
app/miele/lib/service/reship/check.php        # 扩展现有Service
app/miele/dbschema/reship_wo.php              # 扩展数据库表结构（新增2个字段）
app/taskmgr/lib/whitelist/miele.php           # 添加定时任务配置
```

### 8. 配置常量定义

**退货相关配置常量** (在service类中定义，不创建配置表):
```php
<?php
/**
 * 退货配置常量
 * 文件: app/miele/lib/service/reship/config.php
 */
class miele_service_reship_config {

    // 运费物料编码
    const LOGISTICS_FEE_MATERIAL_CODE = 'FREIGHT001';

    // 同步批量大小
    const SYNC_BATCH_SIZE = 50;

    // 七天无理由退款状态
    const IS_7TORETURN_YES = 'yes';         // 是
    const IS_7TORETURN_NO = 'no';           // 否
    const IS_7TORETURN_PENDING = 'pending'; // 待确认

    // 取件类型
    const PICKUP_TYPE_QUALITY_CHECK = '1';  // 上门质检
    const PICKUP_TYPE_SELF_RETURN = '2';    // 消费者自行寄回
    const PICKUP_TYPE_DOOR_PICKUP = '3';    // 上门取件

    // SAP同步状态
    const SAP_SYNC_STATUS_NONE = 'none';
    const SAP_SYNC_STATUS_PENDING = 'pending';
    const SAP_SYNC_STATUS_RUNNING = 'running';
    const SAP_SYNC_STATUS_SUCC = 'succ';
    const SAP_SYNC_STATUS_FAIL = 'fail';

    // 客服状态
    const CS_STATUS_NO_NEED = '0';          // 无需介入
    const CS_STATUS_NEED = '1';             // 需要客服介入
    const CS_STATUS_INVOLVED = '2';         // 客服已经介入
    const CS_STATUS_FIRST_AUDIT = '3';      // 客服初审完成
    const CS_STATUS_REVIEW_FAIL = '4';      // 客服主管复审失败
    const CS_STATUS_COMPLETED = '5';        // 客服处理完成

    // 入库状态
    const INBOUND_STATUS_NOT = '0';         // 未入库
    const INBOUND_STATUS_DONE = '1';        // 已入库
    const INBOUND_STATUS_PROCESSING = '2';  // 入库中
    const INBOUND_STATUS_REJECT = '3';      // 拒绝入库

    /**
     * 获取配置值
     */
    public static function getConfig($key, $default = null) {
        $configs = [
            'logistics_fee_material_code' => self::LOGISTICS_FEE_MATERIAL_CODE,
            'sync_batch_size' => self::SYNC_BATCH_SIZE,
        ];

        return isset($configs[$key]) ? $configs[$key] : $default;
    }
}
```

#### OpenAPI配置扩展

**文件路径**: `app/miele/lib/openapi/conf.php` (扩展现有文件)

```php
<?php
/**
 * 在现有miele_openapi_conf类中新增退货相关接口
 */
class miele_openapi_conf {
    public function getMethods() {
        return array(
            // 现有接口保持不变
            'miele.so' => array(
                'label'   => 'SO单',
                'methods' => array(
                    'updateReserve' => '预约日期更新',
                    'syncCallback' => 'SO创建回调',
                ),
                'group' => 'miele',
            ),
            // ... 其他现有接口

            // 新增退货接口
            'miele.reship' => array(
                'label'   => '退货单',
                'methods' => array(
                    'addCallback' => '退货单创建回调',
                ),
                'group' => 'miele',
            ),
        );
    }
}
```

**OpenAPI参数定义**: `app/miele/lib/openapi/params/v1/reship.php`

```php
<?php
/**
 * 退货单OpenAPI参数定义
 * 类名: miele_openapi_params_v1_reship
 * 文件: app/miele/lib/openapi/params/v1/reship.php
 */
class miele_openapi_params_v1_reship extends openapi_api_params_abstract implements openapi_api_params_interface {

    public function checkParams($method, $params, &$sub_msg) {
        if(parent::checkParams($method, $params, $sub_msg)){
            return true;
        } else {
            return false;
        }
    }

    public function getAppParams($method) {
        $params = array(
            'addCallback' => array(
                'otherSystemSO' => array(
                    'type' => 'string',
                    'required' => 'true',
                    'name' => '退货单号',
                    'desc' => 'OMS系统退货单号'
                ),
                'referenceSO' => array(
                    'type' => 'string',
                    'required' => 'false',
                    'name' => 'SAP退货单号',
                    'desc' => 'SAP系统返回的退货单号'
                ),
                'status' => array(
                    'type' => 'string',
                    'required' => 'true',
                    'name' => '处理状态',
                    'desc' => 'S=成功, E=失败'
                ),
                'message' => array(
                    'type' => 'string',
                    'required' => 'false',
                    'name' => '处理消息',
                    'desc' => '处理结果描述'
                ),
            ),
        );

        return $params[$method];
    }

    public function description($method) {
        $description = array(
            'addCallback' => array(
                'name' => '退货单创建回调',
                'description' => 'SAP系统处理完退货单后的回调通知'
            ),
        );
        return $description[$method];
    }
}
```

**OpenAPI功能实现**: `app/miele/lib/openapi/function/v1/reship.php`

```php
<?php
/**
 * 退货单OpenAPI功能实现
 * 类名: miele_openapi_function_v1_reship
 * 文件: app/miele/lib/openapi/function/v1/reship.php
 */
class miele_openapi_function_v1_reship extends openapi_api_function_abstract implements openapi_api_function_interface {

    function addCallback($params, &$code, &$sub_msg) {
        $res = kernel::single('miele_esb_callback_reship')->addCallback($params);
        return $res;
    }
}
```

### 6. 前端模板实现

#### 补差处理模板 (参考form-design.md)

#### 审核页面模板

**文件路径**: `app/miele/view/admin/reship/audit.html`

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>退货审核</title>
</head>
<body>
<div class="tableform">
    <form method="post" action="index.php?app=miele&ctl=admin_reship&act=audit&id=<{$data.id}>" id="auditForm">

        <!-- 基本信息区块 -->
        <h4>基本信息</h4>
        <div class="division">
            <table>
                <tbody>
                    <tr>
                        <th>退货单号：</th>
                        <td><{$data.reship_bn}></td>
                    </tr>
                    <tr>
                        <th>订单号：</th>
                        <td><{$data.order_bn}></td>
                    </tr>
                    <tr>
                        <th>商品类型：</th>
                        <td><{if $is_large_appliance}>大家电<{else}>小家电<{/if}></td>
                    </tr>
                    <{if $is_forced_refund}>
                    <tr>
                        <td colspan="2" class="c-red">注意：此单为平台强制退款</td>
                    </tr>
                    <{/if}>
                </tbody>
            </table>
        </div>

        <!-- 审核信息区块 -->
        <h4>审核信息</h4>
        <div class="division">
            <table>
                <tbody>
                    <tr>
                        <th><em class="c-red">*</em>7天无理由：</th>
                        <td>
                            <{input type="radio" name="is_7toreturn" value="yes" vtype="required"}> 是
                            <{input type="radio" name="is_7toreturn" value="no"}> 否
                        </td>
                    </tr>
                    <{if !$is_forced_refund}>
                    <tr id="pickup_type_row">
                        <th><em class="c-red">*</em>取件类型：</th>
                        <td>
                            <{if $is_large_appliance}>
                            <{input type="radio" name="pickup_type" value="1" vtype="required"}> 上门质检
                            <{input type="radio" name="pickup_type" value="3"}> 上门取件
                            <{else}>
                            <{input type="radio" name="pickup_type" value="2" vtype="required" checked="checked"}> 消费者自行寄回
                            <{/if}>
                        </td>
                    </tr>
                    <{/if}>
                </tbody>
            </table>
        </div>

        <!-- 联系信息区块 -->
        <div id="contact_info" style="display:none;">
            <h4>联系信息</h4>
            <div class="division">
                <table>
                    <tbody>
                        <tr>
                            <th><em class="c-red">*</em>联系人：</th>
                            <td><{input type="text" name="contact_name" size="20"}></td>
                        </tr>
                        <tr>
                            <th><em class="c-red">*</em>联系电话：</th>
                            <td><{input type="text" name="contact_phone" size="20"}></td>
                        </tr>
                        <tr>
                            <th><em class="c-red">*</em>联系地址：</th>
                            <td><{input type="text" name="contact_address" size="50"}></td>
                        </tr>
                        <tr>
                            <th>需支付物流费用：</th>
                            <td>
                                <{input type="radio" name="logistics_fee_required" value="1"}> 是
                                <{input type="radio" name="logistics_fee_required" value="0" checked="checked"}> 否
                            </td>
                        </tr>
                        <tr id="logistics_fee_row" style="display:none;">
                            <th><em class="c-red">*</em>物流费用：</th>
                            <td><{input type="text" name="logistics_fee" vtype="number" size="10"}> 元</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>

<{area inject=".mainFoot"}>
<div class="table-action">
    <{button label="提交审核" class="btn-primary" id="submitAudit"}>
    <{button label="取消" class="btn-secondary" isCloseDialogBtn="true"}>
</div>
<{/area}>

<script>
$(document).ready(function() {
    // 强制退款默认显示联系信息
    <{if $is_forced_refund}>
    $('#contact_info').show();
    <{/if}>

    // 取件类型变化事件
    $('input[name="pickup_type"]').change(function() {
        if ($(this).val() == '3') { // 上门取件
            $('#contact_info').show();
        } else {
            $('#contact_info').hide();
        }
    });

    // 物流费用选择事件
    $('input[name="logistics_fee_required"]').change(function() {
        if ($(this).val() == '1') {
            $('#logistics_fee_row').show();
        } else {
            $('#logistics_fee_row').hide();
        }
    });

    // 提交审核
    $('#submitAudit').click(function() {
        $('#auditForm').submit();
    });
});
</script>
</body>
</html>
```

#### 质检页面模板

**文件路径**: `app/miele/view/admin/reship/quality_check.html`

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>质检操作</title>
</head>
<body>
<div class="tableform">
    <form method="post" action="index.php?app=miele&ctl=admin_reship&act=quality_check&id=<{$data.id}>" id="qcForm">

        <!-- 基本信息区块 -->
        <h4>基本信息</h4>
        <div class="division">
            <table>
                <tbody>
                    <tr>
                        <th>退货单号：</th>
                        <td><{$data.reship_bn}></td>
                    </tr>
                    <tr>
                        <th>订单号：</th>
                        <td><{$data.order_bn}></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 质检信息区块 -->
        <h4>质检信息</h4>
        <div class="division">
            <table>
                <tbody>
                    <tr>
                        <th><em class="c-red">*</em>质检结果：</th>
                        <td>
                            <{input type="radio" name="qc_result" value="pass" vtype="required"}> 通过
                            <{input type="radio" name="qc_result" value="fail"}> 不通过
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 联系信息区块 -->
        <div id="contact_info" style="display:none;">
            <h4>联系信息</h4>
            <div class="division">
                <table>
                    <tbody>
                        <tr>
                            <th><em class="c-red">*</em>联系人：</th>
                            <td><{input type="text" name="contact_name" size="20"}></td>
                        </tr>
                        <tr>
                            <th><em class="c-red">*</em>联系电话：</th>
                            <td><{input type="text" name="contact_phone" size="20"}></td>
                        </tr>
                        <tr>
                            <th><em class="c-red">*</em>联系地址：</th>
                            <td><{input type="text" name="contact_address" size="50"}></td>
                        </tr>
                        <tr>
                            <th>需支付物流费用：</th>
                            <td>
                                <{input type="radio" name="logistics_fee_required" value="1"}> 是
                                <{input type="radio" name="logistics_fee_required" value="0" checked="checked"}> 否
                            </td>
                        </tr>
                        <tr id="logistics_fee_row" style="display:none;">
                            <th><em class="c-red">*</em>物流费用：</th>
                            <td><{input type="text" name="logistics_fee" vtype="number" size="10"}> 元</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </form>
</div>

<{area inject=".mainFoot"}>
<div class="table-action">
    <{button label="提交质检" class="btn-primary" id="submitQC"}>
    <{button label="取消" class="btn-secondary" isCloseDialogBtn="true"}>
</div>
<{/area}>

<script>
$(document).ready(function() {
    // 质检结果变化事件
    $('input[name="qc_result"]').change(function() {
        if ($(this).val() == 'pass') {
            $('#contact_info').show();
        } else {
            $('#contact_info').hide();
        }
    });

    // 物流费用选择事件
    $('input[name="logistics_fee_required"]').change(function() {
        if ($(this).val() == '1') {
            $('#logistics_fee_row').show();
        } else {
            $('#logistics_fee_row').hide();
        }
    });

    // 提交质检
    $('#submitQC').click(function() {
        $('#qcForm').submit();
    });
});
</script>
</body>
</html>
```

#### 补差处理模板 (参考form-design.md)

**文件路径**: `app/miele/view/admin/reship/bucha.html`

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>补差处理</title>
</head>
<body>
<div class="tableform">
    <form method="post" action="index.php?app=miele&ctl=admin_reship&act=bucha&id=<{$data.id}>" id="buchaForm">

        <!-- 基本信息区块 -->
        <h4>基本信息</h4>
        <div class="division">
            <table>
                <tbody>
                    <tr>
                        <th>退货单号：</th>
                        <td><{$data.reship_bn}></td>
                    </tr>
                    <tr>
                        <th>订单号：</th>
                        <td><{$data.order_bn}></td>
                    </tr>
                    <tr>
                        <th><em class="c-red">*</em>补差金额：</th>
                        <td>
                            <{input type="text" vtype="required&&number" name="bucha_amount" id="buchaAmount" size="20"}> 元
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 订单明细区块 -->
        <h4>订单明细</h4>
        <div class="division">
            <table class="table">
                <thead>
                    <tr>
                        <th>选择</th>
                        <th>物料编码</th>
                        <th>物料名称</th>
                        <th>申请数量</th>
                        <th>申请金额</th>
                        <th>补差金额</th>
                    </tr>
                </thead>
                <tbody>
                    <{foreach from=$items item=item}>
                    <tr>
                        <td>
                            <input type="checkbox" name="items[<{$item.id}>][selected]" value="1" class="item-checkbox">
                        </td>
                        <td><{$item.bn}></td>
                        <td><{$item.product_name}></td>
                        <td><{$item.apply_num}></td>
                        <td><{$item.apply_amount}></td>
                        <td>
                            <{input type="text" vtype="number" name="items[`$item.id`][bucha_amount]" class="item-amount" size="10" value="0"}> 元
                        </td>
                    </tr>
                    <{/foreach}>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="5" class="text-right"><strong>明细小计：</strong></td>
                        <td><span id="totalAmount" class="c-red">0.00</span> 元</td>
                    </tr>
                </tfoot>
            </table>
        </div>
    </form>
</div>

<{area inject=".mainFoot"}>
<div class="table-action">
    <{button label="提交补差" class="btn-primary" id="submitBucha"}>
    <{button label="取消" class="btn-secondary" isCloseDialogBtn="true"}>
</div>
<{/area}>

<script>
$(document).ready(function() {
    // 计算明细小计
    function calculateTotal() {
        var total = 0;
        $('.item-checkbox:checked').each(function() {
            var row = $(this).closest('tr');
            var amount = parseFloat(row.find('.item-amount').val()) || 0;
            total += amount;
        });
        $('#totalAmount').text(total.toFixed(2));
    }

    // 绑定事件
    $('.item-checkbox, .item-amount').on('change', calculateTotal);

    // 提交按钮事件
    $('#submitBucha').click(function() {
        var buchaAmount = parseFloat($('#buchaAmount').val()) || 0;
        var totalAmount = parseFloat($('#totalAmount').text()) || 0;

        // 验证补差金额
        if (buchaAmount <= 0) {
            alert('请输入有效的补差金额！');
            return false;
        }

        // 验证金额一致性
        if (Math.abs(buchaAmount - totalAmount) > 0.01) {
            alert('补差金额与明细小计不一致！');
            return false;
        }

        // 验证至少选择一项
        if ($('.item-checkbox:checked').length == 0) {
            alert('请至少选择一个明细项！');
            return false;
        }

        // 提交表单
        $('#buchaForm').submit();
    });
});
</script>
</body>
</html>
```

### 7. 配置文件更新

#### 定时任务配置

```php
// 在相应的定时任务配置文件中添加
$tasks['miele_reship_sync'] = array(
    'name' => '退货单SAP同步',
    'class' => 'miele_autotask_timer_pushreship',
    'interval' => 300, // 5分钟执行一次
    'enabled' => true,
    'description' => '自动同步退货单到SAP系统'
);
```

#### 菜单配置

```xml
<!-- 在desktop.xml中添加菜单项 -->
<menu>
    <item id="miele_reship_management">
        <name>退货管理</name>
        <controller>admin_reship</controller>
        <action>index</action>
        <permission>miele_reship_view</permission>
        <order>200</order>
        <display>true</display>
    </item>
</menu>
```
```
