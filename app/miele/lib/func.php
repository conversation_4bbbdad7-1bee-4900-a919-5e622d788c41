
<?php

class miele_func {

    const BRANCH_CODE_SEPARATOR = ':';

    public function getBranchParams($branchId) {
        $branchMdl = app::get('ome')->model('branch');
        $branch = $branchMdl->db_dump(['branch_id' => $branchId], 'branch_bn,storage_code');

        if (!$this->validateBranchBn($branch['branch_bn'])) {
            return false;
        }

        $data = explode(self::BRANCH_CODE_SEPARATOR, $branch['branch_bn']);
        $data['storageLocation'] = $branch['storage_code'];
        $data['plant'] = $data[0];
        $data['storageLocation'] = $branch['storage_code'];

        return $data;
    }

    /**
     * 验证仓库编码格式
     * @param string $branchBn仓库编码
     * @return bool
     */
    public function validateBranchBn($branchBn)
    {
        if (empty($branchBn)) {
            return false;
        }

        // 验证格式：工厂代码:库位代码（分割:有两个值即可）
        $parts = explode(self::BRANCH_CODE_SEPARATOR, $branchBn);
        return count($parts) === 2 && !empty(trim($parts[0])) && !empty(trim($parts[1]));
    }

    /**
     * 判断商品是否为B1类商品
     * @param string $materialId 物料ID
     * @return bool
     */
    public static function isB1Product($materialId) {
        if (empty($materialId)) {
            return false;
        }

        $salesMaterialMdl = app::get('material')->model('sales_material');
        $salesMaterial = $salesMaterialMdl->db_dump(['sales_material_bn' => $materialId], 'class_id');

        if (empty($salesMaterial['class_id'])) {
            return false;
        }

        $classifyMdl = app::get('material')->model('customer_classify');
        $classify = $classifyMdl->db_dump(['class_id' => $salesMaterial['class_id']], 'class_name');

        return isset($classify['class_name']) && $classify['class_name'] === 'B1';
    }

    /**
     * 获取订单的默认仓库ID
     * @param string $orderId 订单ID
     * @return int|null 仓库ID
     */
    public static function getDefaultBranch($orderId) {
        if (empty($orderId)) {
            return null;
        }

        $combineObj = kernel::single('omeauto_auto_combine');
        $branchPlugObj = new omeauto_auto_plugin_branch();

        $groups = [];
        $groups[] = array('idx' => '1', 'hash' => '1', 'orders' => [$orderId]);

        $itemObjects = $combineObj->getItemObject($groups);

        foreach ($itemObjects as $item) {
            $confirmRoles = '';
            $branchPlugObj->process($item, $confirmRoles);
            $branch_ids = $item->getBranchId();

            if (!empty($branch_ids)) {
                $branchId = kernel::single('omeauto_branch_choose')->getSelectBid(null, $item);
                if ($branchId) {
                    return $branchId;
                }
            }
        }

        return null;
    }
}