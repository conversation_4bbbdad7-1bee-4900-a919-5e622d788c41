# 任务分解 - 销售订单(SO)处理B1类商品的仓库逻辑优化

## Document Information

- **Feature Name**: 销售订单(SO)处理B1类商品的仓库逻辑优化
- **Version**: 1.0
- **Date**: 2025-01-28
- **Author**: AI Assistant
- **Related Documents**: 
  - Requirements: [requirements.md](requirements.md)
  - Design: [design.md](design.md)

## Implementation Overview

本实现计划将分为三个主要阶段：基础功能开发、订单处理集成和ESB同步优化。采用渐进式开发策略，确保每个阶段都有可验证的交付物，降低集成风险。

### Implementation Strategy
- 先实现B1商品判断的核心逻辑，作为后续功能的基础
- 在订单处理流程中集成仓库分配逻辑，确保数据完整性
- 最后优化ESB同步逻辑，实现条件化的仓库信息传递
- 每个阶段都包含完整的测试和验证

### Development Approach
- **Testing Strategy**: 单元测试驱动开发，每个组件都有对应的测试用例
- **Integration Strategy**: 逐步集成，先验证单个组件，再验证组件间交互
- **Deployment Strategy**: 灰度发布，先在测试环境验证，再逐步推广到生产环境

## Implementation Plan

### Phase 1: 基础功能开发

- [ ] 1. 实现B1商品判断核心逻辑
  - **文件**: `app/miele/lib/func.php`
  - **方法**: 新增 `isB1Product($materialId)` 静态方法
  - **详细设计**:
    ```php
    /**
     * 判断商品是否为B1类商品
     * @param string $materialId 物料ID
     * @return bool true表示B1商品，false表示非B1商品
     */
    public static function isB1Product($materialId) {
        try {
            // 参数验证
            if (empty($materialId)) {
                kernel::log('warning', 'B1Product_Check: materialId is empty');
                return false;
            }
            
            // 查询销售物料信息
            $salesMaterialMdl = app::get('material')->model('sales_material');
            $salesMaterial = $salesMaterialMdl->db_dump(['sales_material_bn' => $materialId], 'class_id');
            
            if (empty($salesMaterial) || !isset($salesMaterial['class_id'])) {
                kernel::log('info', 'B1Product_Check: sales_material not found', ['material_id' => $materialId]);
                return false;
            }
            
            // 查询客户分类信息
            $classifyMdl = app::get('material')->model('customer_classify');
            $classify = $classifyMdl->db_dump(['class_id' => $salesMaterial['class_id']], 'class_name');
            
            if (empty($classify) || !isset($classify['class_name'])) {
                kernel::log('info', 'B1Product_Check: customer_classify not found', ['class_id' => $salesMaterial['class_id']]);
                return false;
            }
            
            $isB1 = ($classify['class_name'] === 'B1');
            kernel::log('info', 'B1Product_Check: result', [
                'material_id' => $materialId,
                'class_id' => $salesMaterial['class_id'],
                'class_name' => $classify['class_name'],
                'is_b1' => $isB1
            ]);
            
            return $isB1;
            
        } catch (Exception $e) {
            kernel::log('error', 'B1Product_Check: exception occurred', [
                'material_id' => $materialId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
    ```
  - **测试要求**: 创建单元测试覆盖正常情况、异常情况和边界条件
  - _Requirements: Requirement 1_

- [ ] 2. 创建B1商品判断的单元测试
  - **文件**: `tests/unit/miele/lib/FuncTest.php`
  - **测试用例**:
    - 测试B1商品返回true
    - 测试非B1商品返回false
    - 测试不存在的物料ID返回false
    - 测试空参数返回false
    - 测试数据库异常情况
  - **详细设计**:
    ```php
    class FuncTest extends PHPUnit_Framework_TestCase {
        public function testIsB1ProductWithValidB1Material() {
            // 测试有效的B1商品
            $result = miele_func::isB1Product('TEST_B1_MATERIAL');
            $this->assertTrue($result);
        }
        
        public function testIsB1ProductWithNonB1Material() {
            // 测试非B1商品
            $result = miele_func::isB1Product('TEST_NON_B1_MATERIAL');
            $this->assertFalse($result);
        }
        
        public function testIsB1ProductWithEmptyParameter() {
            // 测试空参数
            $result = miele_func::isB1Product('');
            $this->assertFalse($result);
        }
        
        public function testIsB1ProductWithNonExistentMaterial() {
            // 测试不存在的物料
            $result = miele_func::isB1Product('NON_EXISTENT_MATERIAL');
            $this->assertFalse($result);
        }
    }
    ```
  - _Requirements: Requirement 1_

### Phase 2: 订单处理集成

- [ ] 3. 增强create_order方法集成B1商品仓库分配
  - **文件**: `app/miele/lib/order.php`
  - **方法**: 修改 `create_order($orderInfo)` 方法
  - **详细设计**:
    ```php
    public function create_order($orderInfo) {
        // 原有逻辑保持不变...
        
        // 新增B1商品仓库分配逻辑
        try {
            $this->assignWarehouseForB1Products($orderInfo);
        } catch (Exception $e) {
            kernel::log('error', 'create_order: B1 warehouse assignment failed', [
                'order_id' => $orderInfo['order_id'],
                'error' => $e->getMessage()
            ]);
            // 不中断订单创建流程，仅记录错误
        }
        
        // 继续原有逻辑...
    }
    
    /**
     * 为B1商品分配仓库
     */
    private function assignWarehouseForB1Products($orderInfo) {
        if (empty($orderInfo['order_id'])) {
            return;
        }
        
        // 获取订单商品信息
        $soItemsMdl = app::get('miele')->model('sap_so_items');
        $soItems = $soItemsMdl->getList('*', ['so_id' => $orderInfo['so_id']]);
        
        foreach ($soItems as $item) {
            // 判断是否为B1商品
            if (miele_func::isB1Product($item['sales_material_bn'])) {
                // 获取默认仓库
                $branchId = $this->getDefaultBranchForB1Product($orderInfo['order_id']);
                
                if ($branchId) {
                    // 更新仓库信息
                    $updateData = ['branch_id' => $branchId];
                    $soItemsMdl->update($updateData, ['id' => $item['id']]);
                    
                    kernel::log('info', 'B1Product_WarehouseAssigned', [
                        'order_id' => $orderInfo['order_id'],
                        'item_id' => $item['id'],
                        'material_bn' => $item['sales_material_bn'],
                        'branch_id' => $branchId
                    ]);
                }
            }
        }
    }
    
    /**
     * 获取B1商品的默认仓库
     */
    private function getDefaultBranchForB1Product($orderId) {
        try {
            $orderCtl = new ome_ctl_admin_order();
            $branchResult = $orderCtl->getDefaultBranch([$orderId]);
            
            // 处理返回结果，获取仓库ID
            if (is_array($branchResult) && !empty($branchResult)) {
                $branchId = reset($branchResult); // 获取第一个仓库ID
                return $branchId;
            }
            
            return null;
        } catch (Exception $e) {
            kernel::log('error', 'getDefaultBranchForB1Product failed', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    ```
  - _Requirements: Requirement 2_

- [ ] 4. 增强update_order方法集成B1商品仓库分配
  - **文件**: `app/miele/lib/order.php`
  - **方法**: 修改 `update_order($order_id)` 方法
  - **详细设计**:
    ```php
    public function update_order($order_id) {
        // 原有逻辑保持不变...
        
        // 新增B1商品仓库分配逻辑
        try {
            $this->updateWarehouseForB1Products($order_id);
        } catch (Exception $e) {
            kernel::log('error', 'update_order: B1 warehouse assignment failed', [
                'order_id' => $order_id,
                'error' => $e->getMessage()
            ]);
            // 不中断订单更新流程，仅记录错误
        }
        
        // 继续原有逻辑...
    }
    
    /**
     * 更新B1商品的仓库分配
     */
    private function updateWarehouseForB1Products($order_id) {
        // 获取SO信息
        $soMdl = app::get('miele')->model('sap_so');
        $soInfo = $soMdl->db_dump(['order_id' => $order_id], '*');
        
        if (empty($soInfo)) {
            return;
        }
        
        // 获取订单商品信息
        $soItemsMdl = app::get('miele')->model('sap_so_items');
        $soItems = $soItemsMdl->getList('*', ['so_id' => $soInfo['so_id']]);
        
        foreach ($soItems as $item) {
            // 判断是否为B1商品且未分配仓库
            if (miele_func::isB1Product($item['sales_material_bn']) && empty($item['branch_id'])) {
                // 获取默认仓库
                $branchId = $this->getDefaultBranchForB1Product($order_id);
                
                if ($branchId) {
                    // 更新仓库信息
                    $updateData = ['branch_id' => $branchId];
                    $soItemsMdl->update($updateData, ['id' => $item['id']]);
                    
                    kernel::log('info', 'B1Product_WarehouseUpdated', [
                        'order_id' => $order_id,
                        'item_id' => $item['id'],
                        'material_bn' => $item['sales_material_bn'],
                        'branch_id' => $branchId
                    ]);
                }
            }
        }
    }
    ```
  - _Requirements: Requirement 3_

### Phase 3: ESB同步优化

- [ ] 5. 优化ESB同步逻辑支持大家电B1品仓库信息传递
  - **文件**: `app/miele/lib/esb/syncso.php`
  - **方法**: 修改 `_getSoItems($so)` 方法
  - **详细设计**:
    ```php
    private function _getSoItems($so) {
        // 原有逻辑获取商品列表...
        $items = []; // 原有的商品数据
        
        // 新增：为大家电B1品添加仓库信息
        foreach ($items as &$item) {
            try {
                // 判断是否为大家电
                $isLargeAppliance = kernel::single('miele_order')->isLargeAppliance($so['order_id']);
                
                // 判断是否为B1商品
                $isB1Product = miele_func::isB1Product($item['materialCode']);
                
                // 如果是大家电B1品，添加仓库信息
                if ($isLargeAppliance && $isB1Product) {
                    $branchInfo = $this->getBranchInfoForItem($item);
                    if ($branchInfo) {
                        $item['warehouse'] = $branchInfo;
                        
                        kernel::log('info', 'ESB_Sync_WarehouseInfo_Added', [
                            'order_bn' => $so['order_bn'],
                            'material_code' => $item['materialCode'],
                            'warehouse_info' => $branchInfo
                        ]);
                    }
                }
            } catch (Exception $e) {
                kernel::log('error', 'ESB_Sync_WarehouseInfo_Failed', [
                    'order_bn' => $so['order_bn'],
                    'material_code' => $item['materialCode'] ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
                // 不中断同步流程，继续处理其他商品
            }
        }
        
        return $items;
    }
    
    /**
     * 获取商品的仓库信息
     */
    private function getBranchInfoForItem($item) {
        try {
            // 从sap_so_items表获取仓库ID
            $soItemsMdl = app::get('miele')->model('sap_so_items');
            $soItem = $soItemsMdl->db_dump([
                'sales_material_bn' => $item['materialCode']
            ], 'branch_id');
            
            if (empty($soItem['branch_id'])) {
                return null;
            }
            
            // 使用miele_func的getBranchParams方法格式化仓库信息
            $branchParams = miele_func::getBranchParams($soItem['branch_id']);
            
            return $branchParams;
        } catch (Exception $e) {
            kernel::log('error', 'getBranchInfoForItem failed', [
                'material_code' => $item['materialCode'],
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }
    ```
  - _Requirements: Requirement 4_

### Phase 4: 测试和验证

- [ ] 6. 创建集成测试
  - **文件**: `tests/integration/miele/OrderB1WarehouseTest.php`
  - **测试场景**:
    - 创建包含B1商品的订单，验证仓库自动分配
    - 更新订单，验证B1商品仓库分配逻辑
    - ESB同步，验证大家电B1品包含仓库信息
    - 非B1商品不受影响的验证
  - _Requirements: All Requirements_

- [ ] 7. 性能测试和优化
  - **测试内容**:
    - B1商品判断的响应时间测试
    - 批量订单处理的性能测试
    - ESB同步性能影响评估
  - **优化策略**:
    - 考虑添加class_id缓存机制
    - 优化数据库查询语句
    - 批量处理时使用IN查询
  - _Requirements: Non-functional Requirements_

- [ ] 8. 部署和文档
  - **部署配置**: 创建灰度发布配置
  - **操作文档**: 更新相关的操作手册
  - **监控配置**: 设置关键指标的监控和告警
  - _Requirements: All Requirements_

---

## Task Planning Guidelines

### 任务执行顺序
1. 必须按照Phase顺序执行，确保依赖关系正确
2. 每个Phase内的任务可以并行执行
3. 每个任务完成后必须通过相应的测试验证

### 质量要求
- 所有代码必须遵循现有的编码规范
- 每个方法都要有详细的注释和错误处理
- 使用kernel::log进行日志记录，便于问题排查
- 所有数据库操作使用现有的模型层，不直接操作SQL

### 风险控制
- 新功能不能影响现有订单处理流程
- 仓库分配失败不能中断订单创建/更新
- ESB同步异常不能影响其他商品的同步

---

## 估算和里程碑

### 时间估算
- **Phase 1**: 2-3天（基础功能开发）
- **Phase 2**: 3-4天（订单处理集成）
- **Phase 3**: 2-3天（ESB同步优化）
- **Phase 4**: 2-3天（测试和验证）
- **总计**: 9-13天

### 关键里程碑
1. **里程碑1**: B1商品判断功能完成并通过测试
2. **里程碑2**: 订单处理集成完成并通过集成测试
3. **里程碑3**: ESB同步优化完成并通过端到端测试
4. **里程碑4**: 全功能测试通过，准备生产部署
