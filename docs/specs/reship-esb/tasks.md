# 任务分解文档

## 文档信息

- **功能名称**: 退货对接SAP
- **版本**: 1.0
- **日期**: 2025-07-22
- **作者**: Kiro AI助手
- **相关文档**: [需求文档](./requirements.md), [技术设计文档](./design.md)

## 任务概述

本任务旨在实现Miele项目退货订单与SAP系统的自动化对接，包括退货工单管理界面、审核质检流程、SAP数据同步和补差订单管理等功能。

## 任务分解

### 阶段1: 数据库和基础架构 (预计2天)

#### 任务1.1: 数据库表结构扩展
**文件**: `app/miele/dbschema/reship_wo.php`
**描述**: 扩展reship_wo表结构，新增必要字段
**工作量**: 0.5天
**依赖**: 无
**验收标准**:
- 新增sap_sync_time字段（时间类型）
- 新增bucha_status字段（枚举类型）
- 添加相应索引
- 字段属性符合schema-config.md规范

#### 任务1.2: 配置常量类创建
**文件**: `app/miele/lib/service/reship/config.php`
**描述**: 创建退货配置常量管理类
**工作量**: 0.5天
**依赖**: 无
**验收标准**:
- 定义所有业务常量（取件类型、SAP同步状态等）
- 提供getConfig()方法
- 包含完整的常量注释

#### 任务1.3: 服务注册配置
**文件**: `app/miele/services.xml`
**描述**: 更新服务注册配置
**工作量**: 0.5天
**依赖**: 任务1.2
**验收标准**:
- Finder服务正确注册
- 遵循class-loading-rules.md规范

#### 任务1.4: 定时任务配置
**文件**: `app/taskmgr/lib/whitelist/miele.php`
**描述**: 添加退货单SAP同步定时任务配置
**工作量**: 0.5天
**依赖**: 无
**验收标准**:
- 添加pushreship任务配置
- 添加pushreshipdomainqueue初始化配置
- 每5分钟执行一次

### 阶段2: 核心业务逻辑 (预计4天)

#### 任务2.1: ESB接口实现
**文件**: `app/miele/lib/esb/syncreship.php`
**描述**: 实现退货SAP同步接口
**工作量**: 1天
**依赖**: 任务1.2
**验收标准**:
- 实现pushReship()方法
- 数据格式化符合SAP接口要求
- 完整的错误处理和日志记录
- 支持大家电/小家电分类

#### 任务2.2: SAP回调处理
**文件**: `app/miele/lib/esb/callback/reship.php`
**描述**: 实现SAP回调处理逻辑
**工作量**: 0.5天
**依赖**: 任务2.1
**验收标准**:
- 实现addCallback()方法
- 更新同步状态和reship单号
- 完整的操作日志记录

#### 任务2.3: 定时任务实现
**文件**: `app/miele/lib/autotask/timer/pushreship.php`
**描述**: 实现退货单自动同步定时任务
**工作量**: 1天
**依赖**: 任务2.1, 任务1.4
**验收标准**:
- 实现process()方法
- 任务锁机制防止并发
- 批量处理待同步退货单
- 完整的错误处理

#### 任务2.4: 服务层业务逻辑扩展
**文件**: `app/miele/lib/service/reship/create.php`
**描述**: 扩展退货创建服务，添加审核和质检处理
**工作量**: 1.5天
**依赖**: 任务1.2, 任务2.5
**验收标准**:
- 实现process_audit()方法
- 实现process_quality_check()方法
- 支持大家电/小家电/平台强制退款差异化处理
- 物流费用补差订单生成
- 完整的操作日志记录

#### 任务2.5: 补差订单服务
**文件**: `app/miele/lib/service/adjustment/so.php`
**描述**: 实现补差订单生成服务
**工作量**: 1天
**依赖**: 任务1.2
**验收标准**:
- 实现generateBuchaOrder()方法
- 实现generateLogisticsFeeOrder()方法
- 支持运费补差和金额补差
- 数据验证和错误处理

### 阶段3: 用户界面开发 (预计3天)

#### 任务3.1: Finder层增强
**文件**: `app/miele/lib/finder/reship.php`
**描述**: 扩展退货工单列表展示和操作按钮
**工作量**: 0.5天
**依赖**: 任务1.1
**验收标准**:
- 实现Tab标签页功能
- 完善操作按钮逻辑（审核/质检/补差）
- SAP同步状态显示
- 按钮显示条件正确

#### 任务3.2: 控制器层扩展
**文件**: `app/miele/controller/admin/reship.php`
**描述**: 扩展退货管理控制器，添加审核质检功能
**工作量**: 1天
**依赖**: 任务2.4
**验收标准**:
- 实现audit()方法
- 实现quality_check()方法
- 扩展bucha()方法
- 商品类型判断逻辑
- 完整的数据验证

#### 任务3.3: 审核页面模板
**文件**: `app/miele/view/admin/reship/audit.html`
**描述**: 创建退货审核页面模板
**工作量**: 0.5天
**依赖**: 任务3.2
**验收标准**:
- 支持大家电/小家电不同选项
- 平台强制退款特殊处理
- 联系信息和物流费用动态显示
- 表单验证和用户体验

#### 任务3.4: 质检页面模板
**文件**: `app/miele/view/admin/reship/quality_check.html`
**描述**: 创建质检操作页面模板
**工作量**: 0.5天
**依赖**: 任务3.2
**验收标准**:
- 质检结果选择
- 质检通过时的联系信息填写
- 物流费用处理
- 表单验证和交互逻辑

#### 任务3.5: 补差页面模板优化
**文件**: `app/miele/view/admin/reship/bucha.html`
**描述**: 优化补差处理页面模板
**工作量**: 0.5天
**依赖**: 任务2.5
**验收标准**:
- 订单明细展示和选择
- 补差金额计算和验证
- 用户友好的操作界面
- 数据一致性验证

### 阶段4: 接口集成 (预计2天)

#### 任务4.1: OpenAPI参数定义
**文件**: `app/miele/lib/openapi/params/v1/reship.php`
**描述**: 定义退货相关OpenAPI参数
**工作量**: 0.5天
**依赖**: 无
**验收标准**:
- 完整的参数定义
- 参数验证规则
- 详细的参数描述

#### 任务4.2: OpenAPI功能实现
**文件**: `app/miele/lib/openapi/function/v1/reship.php`
**描述**: 实现退货相关OpenAPI功能
**工作量**: 0.5天
**依赖**: 任务4.1, 任务2.2
**验收标准**:
- 实现addCallback()方法
- 调用ESB回调处理
- 完整的错误处理

#### 任务4.3: OpenAPI配置扩展
**文件**: `app/miele/lib/openapi/conf.php`
**描述**: 扩展OpenAPI配置，添加退货接口
**工作量**: 0.5天
**依赖**: 任务4.1, 任务4.2
**验收标准**:
- 添加miele.reship接口配置
- 接口方法正确注册
- 符合现有配置规范

#### 任务4.4: 服务层检查逻辑扩展
**文件**: `app/miele/lib/service/reship/check.php`
**描述**: 扩展退货检查服务
**工作量**: 0.5天
**依赖**: 任务1.2
**验收标准**:
- 实现check_sap_sync_ready()方法
- SAP同步前置条件检查
- 完整的业务规则验证

### 阶段5: 测试和优化 (预计2天)

#### 任务5.1: 单元测试
**描述**: 编写核心业务逻辑的单元测试
**工作量**: 1天
**依赖**: 阶段2完成
**验收标准**:
- Service层方法测试覆盖
- ESB接口调用测试
- 数据验证逻辑测试

#### 任务5.2: 集成测试
**描述**: 端到端业务流程测试
**工作量**: 0.5天
**依赖**: 阶段3完成
**验收标准**:
- 审核流程完整性测试
- 质检流程测试
- SAP接口集成测试

#### 任务5.3: 性能优化
**描述**: 系统性能优化和调优
**工作量**: 0.5天
**依赖**: 任务5.2
**验收标准**:
- 接口响应时间优化
- 数据库查询优化
- 定时任务性能调优

## 任务依赖关系

```mermaid
graph TD
    A[阶段1: 数据库和基础架构] --> B[阶段2: 核心业务逻辑]
    B --> C[阶段3: 用户界面开发]
    B --> D[阶段4: 接口集成]
    C --> E[阶段5: 测试和优化]
    D --> E
```

## 风险评估

### 高风险任务
1. **任务2.1**: ESB接口实现 - SAP接口对接复杂度高
2. **任务2.4**: 服务层业务逻辑 - 业务规则复杂，需要仔细处理各种场景
3. **任务3.2**: 控制器层扩展 - 用户交互逻辑复杂

### 风险缓解措施
1. 提前与SAP团队确认接口规范
2. 分步骤实现和测试业务逻辑
3. 充分的用户体验测试

## 时间估算

- **总工作量**: 13天
- **预计工期**: 3周（考虑并行开发和测试时间）
- **关键路径**: 阶段1 → 阶段2 → 阶段3 → 阶段5

## 交付物

1. **代码文件**: 10个新文件 + 7个修改文件
2. **测试用例**: 单元测试和集成测试
3. **部署文档**: 数据库变更脚本和配置说明
4. **用户手册**: 操作流程说明文档
