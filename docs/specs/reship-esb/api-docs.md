# 退货对接SAP - OpenAPI接口文档

## 概述

本文档描述了Miele退货单与SAP系统对接的OpenAPI接口规范，包括接口定义、参数说明、响应格式和测试用例。

## 接口列表

### 1. 退货单创建回调接口

**接口名称**: `miele.reship.addCallback`  
**接口描述**: SAP系统处理完退货单后的回调通知  
**请求方式**: POST  
**接口地址**: `/openapi/v1/miele.reship.addCallback`

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| otherSystemSO | string | 是 | OMS系统退货单号 | R202501220001 |
| sapSystemSO | string | 是 | SAP系统生成的退货单号 | SAP_R_001 |
| status | string | 否 | 处理状态：success/fail | success |
| message | string | 否 | 处理结果消息 | 处理成功 |

#### 响应格式

```json
{
    "rsp": "succ",
    "msg": "回调处理成功"
}
```

#### 错误响应

```json
{
    "rsp": "fail",
    "msg": "退货工单不存在"
}
```

## 测试用例

### 测试用例1: 正常回调处理

**测试场景**: SAP成功处理退货单后回调  
**请求数据**:
```json
{
    "otherSystemSO": "R202501220001",
    "sapSystemSO": "SAP_R_001",
    "status": "success",
    "message": "SAP处理成功"
}
```

**预期响应**:
```json
{
    "rsp": "succ",
    "msg": "回调处理成功"
}
```

**验证点**:
- 退货工单状态更新为同步成功
- SAP退货单号正确保存
- 操作日志正确记录

### 测试用例2: 退货单不存在

**测试场景**: 回调的退货单号在系统中不存在  
**请求数据**:
```json
{
    "otherSystemSO": "R999999999999",
    "sapSystemSO": "SAP_R_002"
}
```

**预期响应**:
```json
{
    "rsp": "fail",
    "msg": "退货工单不存在"
}
```

### 测试用例3: 缺少必填参数

**测试场景**: 请求缺少必填的退货单号  
**请求数据**:
```json
{
    "sapSystemSO": "SAP_R_003"
}
```

**预期响应**:
```json
{
    "rsp": "fail",
    "msg": "退货单号不能为空"
}
```

## 接口调用示例

### cURL示例

```bash
curl -X POST "https://your-domain.com/openapi/v1/miele.reship.addCallback" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -d '{
    "otherSystemSO": "R202501220001",
    "sapSystemSO": "SAP_R_001",
    "status": "success",
    "message": "SAP处理成功"
  }'
```

### PHP示例

```php
<?php
$url = 'https://your-domain.com/openapi/v1/miele.reship.addCallback';
$data = [
    'otherSystemSO' => 'R202501220001',
    'sapSystemSO' => 'SAP_R_001',
    'status' => 'success',
    'message' => 'SAP处理成功'
];

$options = [
    'http' => [
        'header' => [
            'Content-Type: application/json',
            'Authorization: Bearer YOUR_API_TOKEN'
        ],
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);
$response = json_decode($result, true);

if ($response['rsp'] === 'succ') {
    echo "回调处理成功\n";
} else {
    echo "回调处理失败: " . $response['msg'] . "\n";
}
?>
```

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| PARAM_MISSING | 缺少必填参数 | 检查请求参数是否完整 |
| RESHIP_NOT_FOUND | 退货工单不存在 | 确认退货单号是否正确 |
| INVALID_STATUS | 工单状态不允许回调 | 检查工单当前状态 |
| SYSTEM_ERROR | 系统异常 | 联系技术支持 |

## 安全说明

1. **认证**: 所有API调用需要提供有效的API Token
2. **HTTPS**: 生产环境必须使用HTTPS协议
3. **IP白名单**: 建议配置SAP服务器IP白名单
4. **请求频率**: 建议控制请求频率，避免过于频繁的调用

## 监控和日志

1. **接口调用日志**: 所有API调用都会记录到系统日志
2. **错误监控**: 异常情况会触发告警通知
3. **性能监控**: 监控接口响应时间和成功率

## 联系方式

如有技术问题，请联系：
- 技术支持邮箱: <EMAIL>
- 开发团队: <EMAIL>
