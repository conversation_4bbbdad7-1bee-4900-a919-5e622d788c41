# Task 5: 创建综合集成测试

## 任务概述
创建完整的集成测试套件，验证B1商品仓库分配和ESB同步功能的端到端流程。

## 详细设计

### 测试范围
- B1商品判断功能
- 订单创建时的仓库分配
- 订单更新时的仓库分配
- ESB同步时的仓库信息传递

### 测试文件位置
- **测试目录**: `tests/integration/miele/`
- **测试文件**: `B1WarehouseSyncTest.php`

### 核心测试用例

#### 1. 端到端流程测试

```php
<?php

class B1WarehouseSyncTest extends PHPUnit_Framework_TestCase
{
    /**
     * 测试B1商品完整流程：创建订单 -> 分配仓库 -> ESB同步
     */
    public function testB1ProductCompleteFlow()
    {
        // 1. 准备测试数据
        $orderData = $this->createTestOrderWithB1Product();
        
        // 2. 创建订单
        $orderLib = kernel::single('miele_order');
        $result = $orderLib->create_order($orderData);
        
        $this->assertTrue($result['success']);
        
        // 3. 验证B1商品仓库分配
        $soItems = $this->getSoItems($orderData['so_id']);
        $b1Items = array_filter($soItems, function($item) {
            return miele_func::isB1Product($item['sales_material_bn']);
        });
        
        foreach ($b1Items as $item) {
            $this->assertNotEmpty($item['branch_id'], 'B1商品应该分配仓库');
        }
        
        // 4. 测试ESB同步
        $esbSync = new miele_esb_syncso();
        $syncResult = $esbSync->pushSo($orderData['so_id']);
        
        $this->assertEquals('succ', $syncResult['res']);
        
        // 5. 验证ESB数据包含仓库信息
        $this->verifyEsbWarehouseInfo($orderData);
    }
    
    /**
     * 测试非B1商品不受影响
     */
    public function testNonB1ProductNotAffected()
    {
        // 1. 准备非B1商品订单
        $orderData = $this->createTestOrderWithNonB1Product();
        
        // 2. 创建订单
        $orderLib = kernel::single('miele_order');
        $result = $orderLib->create_order($orderData);
        
        $this->assertTrue($result['success']);
        
        // 3. 验证非B1商品仓库不自动分配
        $soItems = $this->getSoItems($orderData['so_id']);
        $nonB1Items = array_filter($soItems, function($item) {
            return !miele_func::isB1Product($item['sales_material_bn']);
        });
        
        foreach ($nonB1Items as $item) {
            $this->assertEmpty($item['branch_id'], '非B1商品不应该自动分配仓库');
        }
    }
    
    /**
     * 测试订单更新时的仓库分配
     */
    public function testOrderUpdateWarehouseAssignment()
    {
        // 1. 创建包含B1商品的订单（不分配仓库）
        $orderData = $this->createTestOrderWithB1Product();
        
        // 2. 更新订单
        $orderLib = kernel::single('miele_order');
        $result = $orderLib->update_order($orderData);
        
        $this->assertTrue($result['success']);
        
        // 3. 验证B1商品仓库分配
        $soItems = $this->getSoItems($orderData['so_id']);
        $b1Items = array_filter($soItems, function($item) {
            return miele_func::isB1Product($item['sales_material_bn']);
        });
        
        foreach ($b1Items as $item) {
            $this->assertNotEmpty($item['branch_id'], 'B1商品应该在更新时分配仓库');
        }
    }
    
    /**
     * 测试大家电B1品ESB同步包含仓库信息
     */
    public function testLargeApplianceB1EsbSync()
    {
        // 1. 创建大家电B1商品订单
        $orderData = $this->createTestLargeApplianceB1Order();
        
        // 2. 创建订单并分配仓库
        $orderLib = kernel::single('miele_order');
        $orderLib->create_order($orderData);
        
        // 3. 模拟ESB同步
        $esbSync = new miele_esb_syncso();
        $soItems = $esbSync->_getSoItems($orderData);
        
        // 4. 验证大家电B1品包含plant和storageLocation
        foreach ($soItems as $item) {
            if ($this->isLargeApplianceB1($item)) {
                $this->assertNotEmpty($item['plant'], '大家电B1品应该包含plant');
                $this->assertNotEmpty($item['storageLocation'], '大家电B1品应该包含storageLocation');
            }
        }
    }
    
    // 辅助方法
    private function createTestOrderWithB1Product()
    {
        return [
            'order_id' => 'TEST_ORDER_' . time(),
            'so_id' => 'TEST_SO_' . time(),
            'items' => [
                [
                    'sales_material_bn' => 'B1_PRODUCT_001',
                    'nums' => 1,
                    'divide_order_fee' => 1000
                ]
            ]
        ];
    }
    
    private function createTestOrderWithNonB1Product()
    {
        return [
            'order_id' => 'TEST_ORDER_' . time(),
            'so_id' => 'TEST_SO_' . time(),
            'items' => [
                [
                    'sales_material_bn' => 'NON_B1_PRODUCT_001',
                    'nums' => 1,
                    'divide_order_fee' => 500
                ]
            ]
        ];
    }
    
    private function getSoItems($soId)
    {
        $soItemsMdl = app::get('miele')->model('sap_so_items');
        return $soItemsMdl->getList('*', ['so_id' => $soId]);
    }
    
    private function isLargeApplianceB1($item)
    {
        return miele_func::isB1Product($item['sales_material_bn']) && 
               kernel::single('miele_order')->isLargeAppliance($item['order_id']);
    }
}
```

### 测试数据准备

#### 1. B1商品测试数据
```sql
-- 插入B1分类
INSERT INTO customer_classify (class_id, class_name) VALUES (1, 'B1');

-- 插入B1商品
INSERT INTO sales_material (sales_material_bn, class_id) VALUES ('B1_PRODUCT_001', 1);
```

#### 2. 非B1商品测试数据
```sql
-- 插入非B1分类
INSERT INTO customer_classify (class_id, class_name) VALUES (2, 'A1');

-- 插入非B1商品
INSERT INTO sales_material (sales_material_bn, class_id) VALUES ('NON_B1_PRODUCT_001', 2);
```

### 验收标准

#### 功能验收
- [ ] B1商品端到端流程测试通过
- [ ] 非B1商品不受影响测试通过
- [ ] 订单更新仓库分配测试通过
- [ ] ESB同步仓库信息测试通过

#### 覆盖率验收
- [ ] 测试覆盖率 >90%
- [ ] 关键路径100%覆盖
- [ ] 异常情况覆盖完整

---

## 实施步骤

### Step 1: 创建测试框架
1. 设置测试环境和数据库
2. 创建测试基类和辅助方法

### Step 2: 实现核心测试用例
1. 端到端流程测试
2. 边界条件测试
3. 异常情况测试

### Step 3: 执行和优化
1. 运行测试套件
2. 修复发现的问题
3. 优化测试性能

---

## 相关文档
- **需求文档**: [requirements.md](requirements.md) - 所有需求
- **设计文档**: [design.md](design.md) - 所有组件
- **Task 1-4**: 所有前置任务的实现
