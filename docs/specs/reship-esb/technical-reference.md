# 退货对接SAP - 技术参考文档

## 文档概述

本文档作为退货对接SAP项目的长期技术参考，详细记录了系统架构、技术实现、接口规范和维护指南，供后续开发和维护人员参考。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    Miele退货管理系统                          │
├─────────────────────────────────────────────────────────────┤
│  用户界面层                                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ 审核页面     │ │ 质检页面     │ │ 补差页面     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│  控制器层                                                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ miele_controller_admin_reship                           │ │
│  │ - audit() 审核处理                                       │ │
│  │ - quality_check() 质检处理                               │ │
│  │ - bucha() 补差处理                                       │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  服务层                                                      │
│  ┌─────────────────┐ ┌─────────────────┐                   │
│  │ reship_create   │ │ adjustment_so   │                   │
│  │ 审核质检业务逻辑  │ │ 补差订单服务     │                   │
│  └─────────────────┘ └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  ESB接口层                                                   │
│  ┌─────────────────┐ ┌─────────────────┐                   │
│  │ syncreship      │ │ callback_reship │                   │
│  │ SAP同步接口      │ │ SAP回调处理      │                   │
│  └─────────────────┘ └─────────────────┘                   │
├─────────────────────────────────────────────────────────────┤
│  数据访问层                                                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ miele_mdl_reship_wo (退货工单模型)                        │ │
│  │ miele_mdl_sap_so (补差订单模型)                           │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      SAP系统                                │
│  ┌─────────────────┐ ┌─────────────────┐                   │
│  │ 退货单处理       │ │ 补差订单处理     │                   │
│  └─────────────────┘ └─────────────────┘                   │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件说明

#### 1. 用户界面层
- **审核页面**: 处理7天无理由审核，支持大家电/小家电差异化流程
- **质检页面**: 处理上门质检结果，支持物流费用录入
- **补差页面**: 处理手动补差订单生成

#### 2. 控制器层
- **Tab功能**: 实现状态分类显示
- **权限控制**: 基于用户角色的操作权限
- **数据验证**: 前端表单验证和后端业务验证

#### 3. 服务层
- **业务逻辑封装**: 将复杂的业务规则封装在服务类中
- **状态管理**: 统一的状态流转控制
- **事务处理**: 确保数据一致性

#### 4. ESB接口层
- **同步接口**: 主动推送数据到SAP
- **回调接口**: 接收SAP处理结果
- **错误处理**: 完善的异常处理和重试机制

## 数据库设计

### 核心表结构

#### reship_wo表扩展字段
```sql
-- SAP集成相关字段
`sap_sync_time` INT(11) DEFAULT NULL COMMENT 'SAP同步时间',
`bucha_status` VARCHAR(20) DEFAULT 'none' COMMENT '补差状态',
`sap_reship_bn` VARCHAR(50) DEFAULT NULL COMMENT 'SAP退货单号',
`sap_sync_status` VARCHAR(20) DEFAULT 'none' COMMENT 'SAP同步状态',

-- 联系信息字段
`contact_name` VARCHAR(100) DEFAULT NULL COMMENT '联系人姓名',
`contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话', 
`contact_address` VARCHAR(500) DEFAULT NULL COMMENT '联系地址',

-- 物流费用字段
`logistics_fee_required` TINYINT(1) DEFAULT 0 COMMENT '是否需要物流费用',
`logistics_fee` DECIMAL(10,2) DEFAULT 0.00 COMMENT '物流费用',

-- 索引设计
KEY `idx_sap_sync_status` (`sap_sync_status`),
KEY `idx_sap_sync_time` (`sap_sync_time`),
KEY `idx_bucha_status` (`bucha_status`)
```

#### sap_so表结构
```sql
CREATE TABLE `miele_sap_so` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `so_id` VARCHAR(50) NOT NULL COMMENT 'SO单号',
  `order_id` INT(11) DEFAULT NULL COMMENT '关联订单ID',
  `order_bn` VARCHAR(50) DEFAULT NULL COMMENT '订单号',
  `source` VARCHAR(20) DEFAULT NULL COMMENT '来源类型',
  `source_id` INT(11) DEFAULT NULL COMMENT '来源ID',
  `order_type` VARCHAR(20) DEFAULT NULL COMMENT '订单类型',
  `bucha_type` VARCHAR(20) DEFAULT NULL COMMENT '补差类型',
  `total_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '总金额',
  `sap_sync_status` VARCHAR(20) DEFAULT 'pending' COMMENT 'SAP同步状态',
  `createtime` INT(11) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_so_id` (`so_id`),
  KEY `idx_source` (`source`, `source_id`),
  KEY `idx_sap_sync_status` (`sap_sync_status`)
) COMMENT='SAP补差订单表';
```

### 状态枚举定义

```php
// SAP同步状态
const SAP_SYNC_STATUS_NONE = 'none';        // 无需同步
const SAP_SYNC_STATUS_PENDING = 'pending';  // 待同步
const SAP_SYNC_STATUS_RUNNING = 'running';  // 同步中
const SAP_SYNC_STATUS_SUCC = 'succ';       // 同步成功
const SAP_SYNC_STATUS_FAIL = 'fail';       // 同步失败

// 补差状态
const BUCHA_STATUS_NONE = 'none';           // 无需补差
const BUCHA_STATUS_PENDING = 'pending';     // 待补差
const BUCHA_STATUS_DONE = 'done';          // 已补差

// 取件类型
const PICKUP_TYPE_DOOR_QC = '1';            // 上门质检
const PICKUP_TYPE_CONSUMER_SEND = '2';      // 消费者自行寄回
const PICKUP_TYPE_DOOR_PICKUP = '3';        // 上门取件
```

## 接口规范

### SAP推送接口

#### 请求格式
```json
{
  "reship_bn": "R202501220001",
  "order_bn": "SO202501220001", 
  "platform_order_bn": "PF202501220001",
  "shop_id": "shop001",
  "branch_id": "branch001",
  "is_7toreturn": "yes",
  "pickup_type": "1",
  "qc_result": "pass",
  "contact_name": "张三",
  "contact_phone": "13800138000",
  "contact_address": "北京市朝阳区xxx",
  "logistics_fee": "50.00",
  "inbound_time": "2025-01-22 10:30:00",
  "items": [
    {
      "bn": "ITEM001",
      "product_name": "商品名称",
      "nums": 1,
      "price": "100.00"
    }
  ]
}
```

#### 响应格式
```json
{
  "success": true,
  "message": "处理成功",
  "data": {
    "sap_reship_bn": "SAP_R_001",
    "process_time": "2025-01-22 10:35:00"
  }
}
```

### SAP回调接口

#### OpenAPI接口路径
```
POST /openapi/v1/miele.reship.addCallback
```

#### 请求参数
```json
{
  "otherSystemSO": "R202501220001",
  "sapSystemSO": "SAP_R_001", 
  "status": "success",
  "message": "处理成功"
}
```

#### 响应格式
```json
{
  "rsp": "succ",
  "msg": "回调处理成功"
}
```

## 业务流程详解

### 1. 大家电审核流程

```mermaid
graph TD
    A[退货单创建] --> B[待审核状态]
    B --> C{7天无理由?}
    C -->|是| D{取件类型选择}
    C -->|否| E[直接同步SAP]
    D -->|上门质检| F[待质检状态]
    D -->|消费者自行寄回| E
    D -->|上门取件| G[填写联系信息]
    G --> H{需要物流费用?}
    H -->|是| I[生成补差SO单]
    H -->|否| E
    I --> E
    F --> J{质检结果}
    J -->|通过| K[填写联系信息]
    J -->|不通过| L[质检不通过]
    K --> M{需要物流费用?}
    M -->|是| N[生成补差SO单]
    M -->|否| E
    N --> E
    E --> O[SAP处理]
    O --> P[回调更新状态]
```

### 2. 补差SO单生成流程

```mermaid
graph TD
    A[触发补差] --> B{检查是否已生成}
    B -->|已存在| C[返回错误]
    B -->|不存在| D[构建SO单数据]
    D --> E[生成SO单号]
    E --> F[创建SO单记录]
    F --> G[创建SO单明细]
    G --> H[记录操作日志]
    H --> I[返回成功]
```

## 关键技术实现

### 1. 状态机实现

```php
class ReshipStateMachine {
    private $allowedTransitions = [
        'pending' => ['auditing', 'rejected'],
        'auditing' => ['qc_pending', 'sync_pending'],
        'qc_pending' => ['qc_pass', 'qc_fail'],
        'qc_pass' => ['sync_pending'],
        'sync_pending' => ['syncing'],
        'syncing' => ['sync_success', 'sync_fail']
    ];
    
    public function canTransition($from, $to) {
        return in_array($to, $this->allowedTransitions[$from] ?? []);
    }
}
```

### 2. 补差SO单生成

```php
public function generateLogisticsFeeOrder($reshipWoId, $logisticsFee, $materialCode) {
    // 1. 防重复检查
    $existingSo = $this->_checkExistingSo($reshipWoId, 'logistics_fee');
    if ($existingSo) {
        return ['res' => 'fail', 'message' => '补差订单已存在'];
    }
    
    // 2. 生成SO单数据
    $soData = $this->_buildSoData($reshipWoId, $logisticsFee);
    
    // 3. 数据库事务处理
    $db = kernel::database();
    $db->beginTransaction();
    
    try {
        $soId = $this->_createSoRecord($soData);
        $this->_createSoItems($soId, $materialCode, $logisticsFee);
        $this->_logOperation($reshipWoId, $logisticsFee);
        
        $db->commit();
        return ['res' => 'succ', 'data' => ['so_id' => $soId]];
        
    } catch (Exception $e) {
        $db->rollBack();
        return ['res' => 'fail', 'message' => $e->getMessage()];
    }
}
```

### 3. 定时任务实现

```php
class miele_autotask_timer_pushreship {
    public function exec() {
        $reshipModel = app::get('miele')->model('reship_wo');
        
        // 获取待同步的退货单
        $pendingReship = $reshipModel->getList('*', [
            'sap_sync_status' => 'pending',
            'inbound_status' => 'done'
        ], 0, 50);
        
        foreach ($pendingReship as $reship) {
            try {
                $syncService = kernel::single('miele_esb_syncreship');
                $result = $syncService->pushReship($reship['id']);
                
                if ($result['res'] == 'succ') {
                    $this->_updateSyncStatus($reship['id'], 'running');
                }
                
            } catch (Exception $e) {
                kernel::log('定时同步失败: ' . $e->getMessage());
            }
        }
    }
}
```

## 监控和维护

### 1. 关键监控指标

- **同步成功率**: SAP同步成功的退货单比例
- **接口响应时间**: SAP接口调用的平均响应时间
- **错误率**: 系统错误和异常的发生频率
- **处理时长**: 从审核到同步完成的平均时长

### 2. 日志记录规范

```php
// 操作日志
$opObj = app::get('ome')->model('operation_log');
$opObj->write_log(
    'operation_type@miele',  // 操作类型
    $entityId,               // 实体ID
    $description             // 操作描述
);

// 系统日志
kernel::log(sprintf(
    '[%s] %s: %s, 数据: %s',
    date('Y-m-d H:i:s'),
    $level,
    $message,
    json_encode($data)
));
```

### 3. 性能优化要点

- **数据库索引**: 为状态字段和时间字段添加索引
- **批量处理**: 定时任务使用批量处理减少数据库访问
- **缓存机制**: 对配置数据和常用查询结果进行缓存
- **连接池**: 使用连接池管理SAP接口连接

## 故障排查指南

### 1. 常见问题及解决方案

#### SAP同步失败
- **检查项**: 网络连接、API密钥、请求格式
- **解决方案**: 查看错误日志，验证接口参数，检查SAP系统状态

#### 补差SO单重复生成
- **检查项**: 防重复逻辑、数据库约束
- **解决方案**: 检查唯一索引，验证业务逻辑

#### 状态流转异常
- **检查项**: 状态机逻辑、并发操作
- **解决方案**: 检查状态验证逻辑，添加数据库锁

### 2. 日志分析

```bash
# 查看SAP同步日志
tail -f /var/log/miele/sap_sync.log

# 查看错误日志
grep "ERROR" /var/log/miele/application.log

# 查看定时任务执行日志
tail -f /var/log/miele/pushreship.log
```

## 扩展和升级

### 1. 功能扩展点

- **批量操作**: 支持批量审核、批量质检
- **工作流引擎**: 引入工作流引擎支持更复杂的审批流程
- **消息通知**: 集成邮件、短信通知功能
- **数据分析**: 添加退货数据分析和报表功能

### 2. 技术升级建议

- **微服务化**: 将ESB接口层独立为微服务
- **消息队列**: 使用消息队列处理异步任务
- **容器化**: 使用Docker容器化部署
- **监控系统**: 集成APM监控系统

## 版本历史

| 版本 | 日期 | 变更内容 | 负责人 |
|------|------|----------|--------|
| 1.0.0 | 2025-01-22 | 初始版本，完整的退货SAP对接功能 | 开发团队 |

## 联系方式

- **技术支持**: <EMAIL>
- **开发团队**: <EMAIL>  
- **文档维护**: <EMAIL>
