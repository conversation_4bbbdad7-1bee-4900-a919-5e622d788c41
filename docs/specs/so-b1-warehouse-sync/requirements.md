# 需求文档 - 销售订单(SO)处理B1类商品的仓库逻辑优化

## Document Information

- **Feature Name**: 销售订单(SO)处理B1类商品的仓库逻辑优化
- **Version**: 1.0
- **Date**: 2025-01-28
- **Author**: AI Assistant
- **Stakeholders**: 销售订单处理团队、ESB集成团队、仓库管理团队

## Introduction

为规范特殊商品（B1品）的订单处理流程，本需求旨在实现两大核心目标：在订单创建与更新阶段为B1类商品自动分配并存储默认仓库，以及在订单同步至下游ESB系统时根据商品特性决定是否包含仓库信息。

### Feature Summary
实现B1类商品在销售订单处理过程中的仓库自动分配和ESB同步时的条件化仓库信息传递。

### Business Value
- 规范B1类商品的订单处理流程，提高订单处理效率
- 确保大家电B1品订单在ESB同步时包含必要的仓库信息
- 减少人工干预，降低订单处理错误率
- 为后续的仓库管理和物流配送提供准确的数据基础

### Scope
**包含范围：**
- SO创建/更新时B1类商品的仓库自动分配逻辑
- SO同步ESB时大家电B1品的仓库信息传递
- B1类商品判断逻辑的实现
- 仓库信息格式化处理

**排除范围：**
- 非B1类商品的仓库分配逻辑修改
- ESB接口的其他功能调整
- 仓库管理系统的内部逻辑修改

## Requirements

### Requirement 1: B1类商品判断逻辑

**User Story:** 作为系统开发者，我需要一个通用的B1类商品判断方法，以便在订单处理的各个环节中准确识别B1类商品。

#### Acceptance Criteria

1. WHEN 调用 miele_func::isB1Product($materialId) 方法 THEN 系统 SHALL 根据物料ID查询 sales_material 表获取 class_id
2. WHEN 获取到 class_id 后 THEN 系统 SHALL 查询 material 数据库的 customer_classify 表检查 class_name 是否为 'B1'
3. IF class_name 等于 'B1' THEN 系统 SHALL 返回 true
4. IF class_name 不等于 'B1' 或查询失败 THEN 系统 SHALL 返回 false

#### Additional Details
- **Priority**: High
- **Complexity**: Medium
- **Dependencies**: sales_material 表和 customer_classify 表的数据完整性
- **Assumptions**: 物料ID有效且存在于 sales_material 表中

### Requirement 2: SO创建时B1类商品仓库自动分配

**User Story:** 作为订单处理系统，我需要在创建销售订单时为B1类商品自动分配默认仓库，以确保订单数据的完整性。

#### Acceptance Criteria

1. WHEN 调用 app/miele/lib/order.php 中的 create_order 方法 THEN 系统 SHALL 遍历订单中的每个商品行项目
2. WHEN 处理每个商品行项目时 THEN 系统 SHALL 调用 miele_func::isB1Product() 判断是否为B1类商品
3. IF 商品被识别为B1类 THEN 系统 SHALL 调用 ome_ctl_admin_order::getDefaultBranch() 获取默认仓库ID
4. WHEN 获取到仓库ID后 THEN 系统 SHALL 将仓库ID存储到 sap_so_items 表的 branch_id 字段中

#### Additional Details
- **Priority**: High
- **Complexity**: High
- **Dependencies**: ome_ctl_admin_order::getDefaultBranch() 方法的可用性
- **Assumptions**: getDefaultBranch() 方法能够正确返回有效的仓库ID

### Requirement 3: SO更新时B1类商品仓库自动分配

**User Story:** 作为订单处理系统，我需要在更新销售订单时为新增或修改的B1类商品自动分配默认仓库，以保持数据一致性。

#### Acceptance Criteria

1. WHEN 调用 app/miele/lib/order.php 中的 update_order 方法 THEN 系统 SHALL 遍历订单中的每个商品行项目
2. WHEN 处理每个商品行项目时 THEN 系统 SHALL 调用 miele_func::isB1Product() 判断是否为B1类商品
3. IF 商品被识别为B1类且当前未分配仓库 THEN 系统 SHALL 调用 ome_ctl_admin_order::getDefaultBranch() 获取默认仓库ID
4. WHEN 获取到仓库ID后 THEN 系统 SHALL 更新 sap_so_items 表的 branch_id 字段

#### Additional Details
- **Priority**: High
- **Complexity**: High
- **Dependencies**: 现有订单数据的完整性
- **Assumptions**: 更新操作不会影响已正确分配仓库的商品

### Requirement 4: ESB同步时大家电B1品仓库信息传递

**User Story:** 作为ESB集成系统，我需要在同步大家电B1品订单时包含仓库信息，以便下游系统进行正确的仓库管理和物流配送。

#### Acceptance Criteria

1. WHEN SO单同步至ESB系统时 THEN 系统 SHALL 检查订单中的每个商品
2. IF 商品是大家电 AND miele_func::isB1Product() 返回 true THEN 系统 SHALL 在同步报文中包含仓库信息
3. WHEN 需要包含仓库信息时 THEN 系统 SHALL 调用 miele_func::getBranchParams($branchId) 获取格式化的仓库参数
4. IF 商品不是大家电B1品 THEN 系统 SHALL 不传递仓库信息，保持现有逻辑

#### Additional Details
- **Priority**: High
- **Complexity**: Medium
- **Dependencies**: miele_func::getBranchParams() 方法的正确实现
- **Assumptions**: 大家电的判断逻辑已存在且可靠

## Non-Functional Requirements

### Performance Requirements
- WHEN 处理单个订单的B1商品判断 THEN 系统 SHALL 在100毫秒内完成响应
- WHEN 批量处理订单时 THEN 系统 SHALL 支持每秒处理至少50个订单

### Security Requirements
- WHEN 访问数据库进行B1商品判断 THEN 系统 SHALL 使用现有的数据库连接和权限控制
- WHEN 调用仓库分配方法 THEN 系统 SHALL 确保只有授权的订单处理流程可以调用

### Usability Requirements
- WHEN B1商品仓库分配失败 THEN 系统 SHALL 记录详细的错误日志便于排查
- WHEN ESB同步包含仓库信息 THEN 系统 SHALL 确保数据格式符合下游系统要求

### Reliability Requirements
- WHEN 数据库查询失败 THEN 系统 SHALL 优雅处理错误并返回明确的失败状态
- WHEN 仓库分配失败 THEN 系统 SHALL 不影响订单的其他处理流程

## Constraints and Assumptions

### Technical Constraints
- 必须遵循现有的 miele 应用架构和编码规范
- 必须使用现有的数据库表结构，不允许修改表结构
- 必须兼容现有的 ESB 同步接口规范

### Business Constraints
- B1类商品的分类标准由业务部门定义，技术实现不得修改分类逻辑
- 仓库分配逻辑必须与现有的仓库管理规则保持一致
- ESB同步的数据格式必须向下兼容

### Assumptions
- sales_material 表和 customer_classify 表的数据准确且及时更新
- ome_ctl_admin_order::getDefaultBranch() 方法能够正确处理B1商品的仓库分配
- 大家电的判断逻辑已存在且稳定可靠
- ESB下游系统能够正确处理包含仓库信息的报文

## Success Criteria

### Definition of Done
- [ ] miele_func::isB1Product() 方法实现并通过单元测试
- [ ] create_order 和 update_order 方法集成B1商品仓库分配逻辑
- [ ] ESB同步逻辑正确处理大家电B1品的仓库信息传递
- [ ] 所有功能通过集成测试和回归测试

### Acceptance Metrics
- B1商品识别准确率达到100%
- 仓库自动分配成功率达到95%以上
- ESB同步包含仓库信息的准确率达到100%
- 系统性能不受新功能影响，响应时间保持在现有水平

## Glossary

| Term | Definition |
|------|------------|
| B1类商品 | 在 customer_classify 表中 class_name 为 'B1' 的商品分类 |
| 大家电 | 通过现有业务逻辑判断的大型家用电器商品 |
| SO单 | Sales Order，销售订单 |
| ESB | Enterprise Service Bus，企业服务总线 |
| 仓库分配 | 为订单商品指定具体的发货仓库 |

---

## Requirements Review Checklist

### Completeness
- [x] 所有用户故事都有明确的角色、功能和价值
- [x] 每个需求都有具体的验收标准使用EARS格式
- [x] 非功能性需求已充分考虑
- [x] 成功标准已定义且可衡量

### Quality
- [x] 需求使用主动语态编写
- [x] 每个验收标准都可测试
- [x] 需求避免了实现细节
- [x] 术语在整个文档中保持一致

### EARS Format Validation
- [x] WHEN 语句描述了具体的事件或触发条件
- [x] IF 语句描述了明确的条件或状态
- [x] 所有语句都使用 SHALL 来描述系统响应

### Clarity
- [x] 需求表述明确无歧义
- [x] 技术术语在词汇表中有解释
- [x] 利益相关者能够理解所有需求
- [x] 不存在冲突的需求

### Traceability
- [x] 需求已编号和组织
- [x] 需求间的依赖关系明确
- [x] 需求与业务目标相关联
- [x] 假设和约束已记录

---

## 约束与依赖

- **技术约束**: 必须遵循项目现有的 docs/cheatsheet 手册和架构。
- **外部依赖**: 依赖 sales_material 表和 customer_classify 表的数据完整性。
