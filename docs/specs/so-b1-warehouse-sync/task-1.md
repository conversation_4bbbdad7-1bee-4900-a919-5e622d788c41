# Task 1: 实现B1商品判断核心逻辑

## 任务概述
在 `app/miele/lib/func.php` 中实现 `isB1Product($materialId)` 静态方法和 `getDefaultBranch($orderId)` 静态方法。

## 详细设计

### 文件位置
- **目标文件**: `app/miele/lib/func.php`
- **新增方法**: `public static function isB1Product($materialId)`, `public static function getDefaultBranch($orderId)`

### 1. B1商品判断方法

```php
/**
 * 判断商品是否为B1类商品
 * @param string $materialId 物料ID
 * @return bool
 */
public static function isB1Product($materialId) {
    if (empty($materialId)) {
        return false;
    }

    $salesMaterialMdl = app::get('material')->model('sales_material');
    $salesMaterial = $salesMaterialMdl->db_dump(['sales_material_bn' => $materialId], 'class_id');

    if (empty($salesMaterial['class_id'])) {
        return false;
    }

    $classifyMdl = app::get('material')->model('customer_classify');
    $classify = $classifyMdl->db_dump(['class_id' => $salesMaterial['class_id']], 'class_name');

    return isset($classify['class_name']) && $classify['class_name'] === 'B1';
}
```

### 2. 获取默认仓库方法

基于 `ome_ctl_admin_order::getDefaultBranch` 的逻辑重新实现：

```php
/**
 * 获取订单的默认仓库ID
 * @param string $orderId 订单ID
 * @return int|null 仓库ID
 */
public static function getDefaultBranch($orderId) {
    if (empty($orderId)) {
        return null;
    }

    $combineObj = kernel::single('omeauto_auto_combine');
    $branchPlugObj = new omeauto_auto_plugin_branch();

    $groups = [];
    $groups[] = array('idx' => '1', 'hash' => '1', 'orders' => [$orderId]);

    $itemObjects = $combineObj->getItemObject($groups);

    foreach ($itemObjects as $key => $item) {
        $confirmRoles = '';
        $branchPlugObj->process($item, $confirmRoles);
        $branch_ids = $item->getBranchId();

        if (!empty($branch_ids)) {
            $branchId = kernel::single('omeauto_branch_choose')->getSelectBid(null, $item);
            if ($branchId) {
                return $branchId;
            }
        }
    }

    return null;
}
```

### 测试用例设计

#### 正常情况测试
1. **B1商品测试**: 返回true
2. **非B1商品测试**: 返回false
3. **默认仓库获取**: 返回有效仓库ID

#### 异常情况测试
1. **空参数测试**: 返回false/null
2. **不存在物料测试**: 返回false
3. **不存在订单测试**: 返回null

### 验收标准

#### 功能验收
- [ ] B1商品正确识别为true
- [ ] 非B1商品正确识别为false
- [ ] 默认仓库正确获取
- [ ] 异常情况正确处理

---

## 实施步骤

### Step 1: 代码实现
1. 在 `app/miele/lib/func.php` 中添加两个静态方法
2. 确保代码格式符合项目规范

### Step 2: 测试验证
1. 测试B1商品判断功能
2. 测试默认仓库获取功能
3. 验证异常情况处理

---

## 相关文档
- **需求文档**: [requirements.md](requirements.md) - Requirement 1
- **设计文档**: [design.md](design.md) - Component 1
