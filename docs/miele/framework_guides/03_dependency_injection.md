# 内部框架: 依赖注入与服务速查表

## 1. 场景与目标

- **解决了什么问题**: 本速查表解释了框架的依赖注入和服务管理方法。它涵盖了如何定义、注册和在整个应用程序中使用服务。该框架使用服务定位器模式而不是传统的构造函数注入。
- **最终产出**: 一个注册到框架中的新的、可重用的服务类，可以从应用程序的任何部分访问。

---

## 2. 先决条件

- 理解服务定位器模式。
- 有权限修改 `app/miele/services.xml` 文件。

---

## 3. 标准执行步骤

### **步骤 1: 创建服务类**

服务只是一个封装了特定功能的 PHP 类。它不需要扩展任何特定的基类。

- **操作**: 在 `app/miele/lib/` 目录中创建一个新的 PHP 类文件。类名应以应用名称为前缀（例如 `miele_`）。

- **代码示例**: 创建一个用于自定义计算的新服务。

  ```php
  // app/miele/lib/mycustomcalculator.php
  <?php

  class miele_mycustomcalculator
  {
      public function add($a, $b)
      {
          // 您可以从服务内部访问其他服务
          $logger = kernel::single('miele_operation_log');
          $logger->log('正在添加两个数字。');

          return $a + $b;
      }
  }
  ```

### **步骤 2: 在 `services.xml` 中注册服务**

所有服务都必须在 `app/miele/services.xml` 中注册，才能对框架可用。

- **操作**: 打开 `app/miele/services.xml` 并添加一个新的 `<service>` 条目。
    - `id`: 服务的唯一标识符。您将使用它来检索服务。
    - `class`: 服务的 PHP 类的名称。

- **代码示例**: 注册 `miele_mycustomcalculator` 类。

  ```xml
  <!-- app/miele/services.xml -->
  <services>
      <!-- ... 现有服务 ... -->
      <service id="miele.my_calculator">
          <class>miele_mycustomcalculator</class>
      </service>
  </services>
  ```

### **步骤 3: 访问服务**

框架提供了两种主要方式来访问注册的服务：`kernel::single()` 和 `app::get()`。

- **`kernel::single('service_id')`**: 这是检索服务的主要方法。它充当单例工厂，意味着它将在第一次调用时实例化类，并在后续调用中返回相同的实例。

- **`app::get('app_name')`**: 用于获取应用程序对象本身，您可以从中访问模型和其他特定于应用的资源。

- **操作**: 使用您在 XML 文件中定义的服务 ID 调用 `kernel::single()`。

- **代码示例**: 在控制器中使用计算器服务。

  ```php
  // 在控制器方法中
  public function calculate()
  {
      // 获取服务实例
      $calculator = kernel::single('miele.my_calculator');

      $result = $calculator->add(5, 10);

      echo "结果是: " . $result;
  }
  ```

---

## 4. 最佳实践与注意事项

- **服务 ID 命名**: 为服务 ID 使用一致的、带命名空间的命名约定（例如 `app_name.service_name`）以避免冲突。
- **`kernel::single()` vs. `new`**: 始终使用 `kernel::single()` 获取服务实例，而不是用 `new` 创建它们。这遵循了单例模式，并允许框架管理对象的生命周期。
- **事件监听器**: 服务的一个常见用途是充当事件监听器。`services.xml` 中的服务 `id` 可以设置为事件名称（例如 `ome.service.order.create.after`），以在该事件被触发时自动调用服务的方法。
- **无构造函数注入**: 框架不支持自动构造函数依赖注入。服务内的依赖项必须使用 `kernel::single()` 手动检索。

---

## 5. 来源与参考

- **源分析**: 对 `app/miele/services.xml`、`app/miele/controller/admin/sales/material/setmapping.php` 以及整个代码库中 `kernel::single()` 的通用用法的分析。