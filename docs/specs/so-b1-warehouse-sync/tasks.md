# SO B1商品仓库同步功能 - 任务分解

## 项目概述

实现B1类商品在销售订单(SO)中的自动仓库分配和ESB同步优化功能：

1. **B1商品识别**: 通过商品分类自动识别B1类商品
2. **仓库自动分配**: 为B1商品自动分配默认仓库
3. **ESB同步优化**: 大家电B1商品在ESB同步时传递仓库信息

## 任务分解

### Task 1: 实现B1商品判断和默认仓库获取逻辑 (1天)
- **文件**: `app/miele/lib/func.php`
- **方法**: `isB1Product($materialId)`, `getDefaultBranch($orderId)`
- **功能**: B1商品判断和默认仓库获取

### Task 2: 增强create_order方法集成B1商品仓库分配 (2天)
- **文件**: `app/miele/lib/order.php`
- **方法**: `create_order()`, `assignWarehouseForB1Products()`
- **功能**: 订单创建时为B1商品自动分配仓库
- **依赖**: Task 1

### Task 3: 优化ESB同步逻辑支持大家电B1品仓库信息传递 (1天)
- **文件**: `app/miele/lib/esb/syncso.php`
- **方法**: `_getSoItems()`
- **功能**: 大家电B1品ESB同步时设置plant和storageLocation
- **依赖**: Task 1

### Task 4: 增强update_order方法集成B1商品仓库分配 (1天)
- **文件**: `app/miele/lib/order.php`
- **方法**: `update_order()`
- **功能**: 订单更新时为B1商品自动分配仓库
- **依赖**: Task 2

### Task 5: 创建综合集成测试 (2天)
- **文件**: `tests/integration/miele/B1WarehouseSyncTest.php`
- **功能**: 端到端测试覆盖所有功能点
- **依赖**: Task 1-4

### Task 6: 部署配置和文档完善 (1天)
- **功能**: 生产环境配置、用户文档、运维文档
- **依赖**: Task 1-5

## 实施原则

1. **最小侵入**: 不修改现有核心逻辑
2. **异常安全**: 新功能异常不影响原有流程
3. **代码简洁**: 避免不必要的复杂性
4. **向下兼容**: 保持与现有功能完全兼容

## 时间估算

**总计**: 8天

## 验收标准

### 功能验收
- [ ] B1商品在订单创建/更新时自动分配默认仓库
- [ ] 大家电B1商品在ESB同步时包含plant和storageLocation
- [ ] 非B1商品不受影响
- [ ] 异常情况不影响订单正常流程

### 质量验收
- [ ] 集成测试通过率100%
- [ ] 文档完整且准确

## 相关文档

- **需求文档**: [requirements.md](requirements.md)
- **技术设计**: [design.md](design.md)
- **详细任务**:
  - [Task 1: B1商品判断和默认仓库逻辑](task-1.md)
  - [Task 2: create_order集成](task-2.md)
  - [Task 3: ESB同步优化](task-3.md)
  - [Task 4: update_order集成](task-4.md)
  - [Task 5: 集成测试](task-5.md)
  - [Task 6: 部署文档](task-6.md)