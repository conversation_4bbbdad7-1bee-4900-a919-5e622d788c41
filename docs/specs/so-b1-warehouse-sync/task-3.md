# Task 3: 优化ESB同步逻辑支持大家电B1品仓库信息传递

## 任务概述
在 `app/miele/lib/esb/syncso.php` 中优化ESB同步逻辑，为大家电B1品添加plant和storageLocation字段。

## 详细设计

### 文件位置
- **目标文件**: `app/miele/lib/esb/syncso.php`
- **修改方法**: `_getSoItems($so)`

### 核心实现逻辑

#### 1. 修改商品数据获取方法

```php
private function _getSoItems($so) {
    $isLargeAppliance = kernel::single('miele_order')->isLargeAppliance($so['order_id']);

    $soItemsList = app::get('miele')->model('sap_so_items')->getList('*', ['so_id' => $so['id']]);
    $soItems = array();
    foreach ($soItemsList as $item) {
        // 小家电，没有仓库，则不推
        if (!$isLargeAppliance && empty($item['branch_id'])) {
            continue;
        }

        $itemData = [
            'otherSystemItem' => $item['id'],
            'higherLevel' => '', // 组合商品，则是销售物料编码
            'material' => $item['bn'],
            'unitPrice' => bcdiv($item['divide_order_fee'], $item['nums'], 2),
            'quantity' => $item['nums'],
            'plant' => '', // 工厂
            'shippingPoint' => '', // 用于区分专车/快递
            'storageLocation' => '', // 库位
        ];

        // 为大家电B1品设置plant和storageLocation
        if ($isLargeAppliance && miele_func::isB1Product($item['sales_material_bn']) && !empty($item['branch_id'])) {
            $branchParams = miele_func::getBranchParams($item['branch_id']);
            if ($branchParams) {
                $itemData['plant'] = $branchParams['plant'];
                $itemData['storageLocation'] = $branchParams['storageLocation'];
            }
        }

        // 其他原有逻辑保持不变...
        if ($item['bn'] != $item['sales_material_bn']) {
            $itemData['higherLevel'] = $item['sales_material_bn'];
        }

        if ($item['item_line_no']) {
            $itemData['itemS4'] = $item['item_line_no'];
        }

        if ($item['deliveryDate']) {
            $itemData['deliveryDate'] = date('Ymd', $item['reserved_time']);
        } else {
            if ($isLargeAppliance) {
                $itemData['deliveryDate'] = date('Ymd', $so['reserved_time']);
            } else {
                $itemData['deliveryDate'] = date('Ymd');
            }
        }

        if ($item['is_del'] == 'true') {
            $itemData['rejected'] = '00';
        }

        $soItems[] = $itemData;
    }
    return $soItems;
}
```

### ESB数据格式变化

#### 原有商品数据格式
```json
{
    "material": "MATERIAL_001",
    "quantity": 2,
    "unitPrice": 1000.00,
    "plant": "",
    "storageLocation": ""
}
```

#### 增强后的大家电B1品数据格式
```json
{
    "material": "MATERIAL_001",
    "quantity": 2,
    "unitPrice": 1000.00,
    "plant": "9600",
    "storageLocation": "ST001"
}

### 测试用例设计

#### 正常流程测试
1. **大家电B1品包含plant和storageLocation**: 验证字段正确设置
2. **非大家电B1品字段为空**: 验证条件判断正确
3. **非B1商品字段为空**: 验证B1判断逻辑

#### 异常情况测试
1. **仓库信息缺失**: 验证字段为空
2. **getBranchParams失败**: 验证降级处理

### 验收标准

#### 功能验收
- [ ] 大家电B1品在ESB同步时包含plant和storageLocation
- [ ] 非大家电B1品的plant和storageLocation为空
- [ ] 异常情况不影响ESB同步成功

---

## 实施步骤

### Step 1: 修改_getSoItems方法
1. 在商品数据处理中添加B1判断
2. 为大家电B1品设置plant和storageLocation字段

### Step 2: 测试验证
1. 测试大家电B1品字段设置
2. 验证非B1商品不受影响

---

## 相关文档
- **需求文档**: [requirements.md](requirements.md) - Requirement 4
- **设计文档**: [design.md](design.md) - Component 3
- **Task 1**: [task-1.md](task-1.md) - 依赖的B1商品判断逻辑
