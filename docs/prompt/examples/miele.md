
  角色: 你是一名资深的软件架构师，擅长代码分析、文档撰写和知识沉淀。

  目标: 请深入分析位于 `app/miele`
  目录下的PHP应用模块，并生成一套完整的、用于未来重构的技术和业务文档。所有文档都应以Markdown格式创建，并存放在新建的 docs/miele/
  目录下。特别地，框架的使用指南需要被拆分成多个独立的、按功能组织的文件，以便于AI后续加载和使用。

  背景与上下文:
  app/miele 是一个现有系统中的核心业务模块。当前任务是为了对该模块进行未来的现代化重构做准备，因此需要一套高质量的文档来清晰地阐述其内部技术实现、业务逻辑和外部接口。

  任务分解 (Step-by-Step Tasks):

   1. 分析代码并创建目录:
       * 首先，请全面分析 app/miele 目录下的所有源代码。
       * 在项目根目录下创建 docs/miele/ 目录，用于存放所有生成的文档。

   2. 生成技术架构文档 (Technical Documentation):
       * 在 docs/miele/technical_documentation.md 文件中，详细描述该模块的技术架构。内容应至少包括：
           * 核心职责: 模块的主要功能和边界是什么。
           * 架构模式: 采用的是MVC、领域驱动设计（DDD）、还是其他模式？
           * 关键组件/类: 列出核心的类、服务、控制器等，并说明它们各自的职责。
           * 数据模型: 分析与数据库的交互，描述主要的数据库表和数据实体。
           * 外部依赖: 模块依赖了项目内哪些其他模块或第三方库。
           * 配置: 模块的配置文件在哪里，有哪些重要配置项。

   3. 梳理业务场景文档 (Business Scenarios):
       * 在 docs/miele/business_scenarios.md 文件中，整理出该模块所支持的核心业务流程和场景。
       * 以用户故事（User Stories）或用例（Use Cases）的形式进行描述。

   4. 提取接口文档 (API Documentation):
       * 在 docs/miele/api_reference.md 文件中，生成该模块对外暴露的API接口文档。
       * 对于每一个接口，请提供URL、HTTP方法、请求参数、成功/失败响应示例和权限要求。

   5. 生成模块化框架指南 (Modular Framework Guides):
       * 目标: 为方便AI后续精确加载和使用，将框架知识分解为按功能组织的、独立的Markdown文件。
       * 识别框架: 首先，识别 app/miele 模块所依赖的核心PHP框架（无论是通用框架还是自研框架）。
       * 创建子目录: 在 docs/miele/ 下创建一个名为 framework_guides/ 的新目录。
       * 分解功能并创建文档: 分析此框架在 app/miele 中的主要应用方式，并为每个核心功能或设计模式创建一个独立的Markdown指南文件。每个指南文件都应参考
         `docs/prompt/templates/cheatsheet-template.md` 的结构来编写。
       * 建议的指南文件列表 (请根据实际分析进行调整):
           * framework_guides/01_routing_and_controllers.md: 如何定义路由和创建控制器。
           * framework_guides/02_database_and_orm.md: 数据库查询、ORM模型定义和使用方法。
           * framework_guides/03_dependency_injection.md: 服务容器和依赖注入的使用方式。
           * framework_guides/04_authentication_and_auth.md: 认证和授权的实现方式。
           * framework_guides/05_common_helpers.md: 常用辅助函数列表和用法。
           * framework_guides/06_view_and_template.md: 视图和模板引擎的使用。
           * framework_guides/07_error_and_logging.md: 错误处理和日志记录机制。

  最终交付产物 (Final Deliverables):

  请确保在任务结束时，项目结构如下：

    1 docs/
    2 └── miele/
    3     ├── technical_documentation.md
    4     ├── business_scenarios.md
    5     ├── api_reference.md
    6     └── framework_guides/
    7         ├── 01_routing_and_controllers.md
    8         ├── 02_database_and_orm.md
    9         ├── 03_dependency_injection.md
   10         ├── ... (其他功能指南)


## 为什么这个提示词更好？

   1. 角色扮演: 明确了AI的角色，有助于其产出更专业的内容。
   2. 结构化: 将一个复杂任务分解为5个独立的、清晰的步骤，逻辑性强，易于执行。
   3. 明确的产出物: 对每个文档的内容和文件名都做了具体规定，避免了模糊不清。
   4. 条件逻辑: 对“速查表”的处理增加了条件判断，使指令更具鲁棒性。
   5. 上下文清晰: 提供了任务的背景（为了重构），这能帮助AI更好地理解“为什么”要这么做，从而生成更具洞察力的内容。