# 内部框架: 数据库与模型 (ORM) 速查表

## 1. 场景与目标

- **解决了什么问题**: 本速查表解释了如何创建和使用模型与数据库进行交互。它涵盖了创建模型类、定义数据库模式以及执行基本的 CRUD (创建、读取、更新、删除) 操作。
- **最终产出**: 一个可用于查询和操作特定数据库表数据的新模型类，以及相应的表模式定义。

---

## 2. 先决条件

- 您要交互的数据库表应该已存在或已定义。
- 具备基本的 SQL 知识。

---

## 3. 标准执行步骤

### **步骤 1: 在 `dbschema` 中定义数据库模式**

框架使用 `app/[app_name]/dbschema/` 目录中的 XML 文件来定义表结构。

- **操作**: 在 `app/miele/dbschema/` 中创建一个新的 `.xml` 文件。文件名应该是表的名称。

- **代码示例**: 定义一个名为 `miele_my_custom_table` 的表。

  ```xml
  <!-- app/miele/dbschema/my_custom_table.xml -->
  <table name="my_custom_table">
    <version>1.0</version>
    <engine>innodb</engine>
    <comment>我的自定义表示例</comment>
    <columns>
      <column name="id" type="number" primary="true" autoincrement="true">
        <comment>主键</comment>
      </column>
      <column name="name" type="varchar(255)">
        <comment>名称</comment>
      </column>
      <column name="created_at" type="time">
        <comment>创建时间戳</comment>
      </column>
    </columns>
  </table>
  ```

### **步骤 2: 创建模型文件和类**

模型负责与其对应的数据库表的所有交互。

- **约定**: 模型类命名为 `[app_name]_mdl_[model_name]`。对于表 `my_custom_table`，模型名将是 `my_custom_table`。
- **基类**: 所有模型都必须扩展 `dbeav_model`，它提供了核心的数据库功能。

- **操作**: 在 `app/miele/model/` 目录中创建新的模型文件。

- **代码示例**:

  ```php
  // app/miele/model/my_custom_table.php
  <?php

  class miele_mdl_my_custom_table extends dbeav_model
  {
      // $defaultOrder 属性可用于为查询设置默认排序顺序。
      public $defaultOrder = 'created_at DESC';

      /**
       * 为特定查询添加自定义方法是一种最佳实践，
       * 可以将业务逻辑保留在模型内部。
       */
      public function findByName($name)
      {
          return $this->getList('*', ['name' => $name]);
      }
  }
  ```

### **步骤 3: 在控制器中使用模型**

模型创建后，您可以在控制器中使用它来执行数据库操作。

- **操作**:
  1. 使用 `app::get('miele')` 获取应用程序的实例。
  2. 使用 `model()` 方法获取模型的实例。
  3. 调用由 `dbeav_model` 基类提供的 CRUD 方法。

- **代码示例**:

  ```php
  // 在控制器方法中
  public function someAction()
  {
      // 获取模型实例
      $myCustomModel = app::get('miele')->model('my_custom_table');

      // CREATE: 保存一条新记录
      $newRecord = [
          'name' => '测试名称',
          'created_at' => time(),
      ];
      $myCustomModel->save($newRecord);

      // READ: 获取记录列表
      $allRecords = $myCustomModel->getList('*', /* 过滤数组 */);

      // READ: 获取单条记录 (dump)
      $singleRecord = $myCustomModel->dump(['id' => 1]);

      // UPDATE: 如果存在主键，save 方法会处理更新
      $singleRecord['name'] = '更新后的名称';
      $myCustomModel->save($singleRecord);

      // DELETE: 删除一条记录
      $myCustomModel->delete(['id' => 1]);
  }
  ```

---

## 4. 最佳实践与注意事项

- **模型命名**: 传递给 `app::get('miele')->model('my_custom_table')` 的模型名必须与文件名 `my_custom_table.php` 匹配。
- **`dbeav_model`**: 这是所有数据库操作的基础模型。建议探索其方法（`dump`, `getList`, `count`, `save`, `delete` 等）以了解 ORM 的全部功能。
- **原生 SQL**: 对于复杂的查询，您可以直接使用 `kernel::database()` 获取数据库连接并运行原生 SQL，但应尽可能避免这样做。
- **事务**: 对于需要事务完整性的操作，请使用 `kernel::database()->beginTransaction()`、`commit()` 和 `rollBack()`。

---

## 5. 来源与参考

- **源分析**: 对 `app/miele/model/stock.php`、`app/miele/dbschema/` 和 `app/miele/controller/admin/sales/material/setmapping.php` 的分析。