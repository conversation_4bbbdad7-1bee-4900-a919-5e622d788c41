# 里程碑1: 核心库存同步服务重构 技术要点摘要

> **目标**: 为AI执行任务提供关键的技术上下文，聚焦于最重要的架构决策、接口定义、数据模型和关键约束。

## 🎯 核心设计决策

### **职责分离原则**
- `syncStock`方法：只负责ESB库存数据获取和存储
- `generateAdjustOrders`方法：独立处理库存调整单生成
- 定时任务：自动调用两个方法
- 手动操作：可选择性调用

### **仓库编码字段使用**
- **使用字段**: `branch_bn`（不是storage_code）
- **格式要求**: "工厂编码:货位编码"（如：9601:1000）
- **验证逻辑**: `explode(':', $branchBn)`后必须有且仅有2个非空部分

## 🔧 关键方法签名

### **syncStock方法**
```php
/**
 * 执行库存同步（只负责数据获取和存储）
 * @param array $materialBns 基础物料编码数组
 * @param array $options 可选参数
 * @return array [bool $success, string $message, array $data]
 */
public function syncStock($materialBns, $options = [])
```

### **关键私有方法**
- `_getValidBranches($options = [])`: 获取有效发货仓库，使用branch_bn验证
- `_validateBranchCode($branchCode)`: 验证branch_bn格式
- `_parseBranchCode($branchCode)`: 解析branch_bn为plant和storageLocation
- `_updateHistoryBatchStatus($currentBatchNo, $options = [])`: 更新历史数据状态

## 📊 数据流程

### **仓库筛选条件**
```php
[
    'is_deliv_branch' => 'true',  // 发货仓库
    'type' => 'main',             // 主仓
    'disabled' => 'false'         // 启用状态
]
```

### **批次号格式**
- **格式**: `STOCK + YYYYMMDD + 6位递增序号`
- **示例**: `STOCK202501201000001`
- **生成服务**: `eccommon_guid->incId('stock_sync_batch', $prefix, 6, true)`

### **状态管理**
- **状态字段**: `sync_status`
- **可选值**: `pending`, `processing`, `success`, `failed`, `none`
- **历史数据更新**: 只更新`pending`和`failed`状态的记录为`none`

## 🔗 ESB接口调用

### **接口方法**
- **方法名**: `miele.qimen.esb.stock.query`
- **参数格式**:
```php
[
    'system' => 'OMS',
    'plant' => '9601',              // 从branch_bn解析
    'storageLocation' => '1000',    // 从branch_bn解析
    'item' => [
        ['material' => 'MAT001'],
        ['material' => 'MAT002'],
    ]
]
```

## ⚠️ 关键约束

### **并发控制**
- 使用Redis分布式锁防止重复同步
- 锁超时时间：5分钟
- 锁粒度：全局锁

### **错误处理**
- 单个仓库同步失败不影响其他仓库
- 参数验证异常直接返回错误
- 系统异常记录详细日志

### **返回值格式**
```php
// 成功
[true, "库存数据同步完成", ['batch_no' => $batchNo]]

// 失败
[false, "错误信息", []]
```

---
