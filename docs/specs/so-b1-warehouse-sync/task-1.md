# Task 1: 实现B1商品判断核心逻辑

## 任务概述
在 `app/miele/lib/func.php` 中实现 `isB1Product($materialId)` 静态方法，作为整个功能的基础组件。

## 详细设计

### 文件位置
- **目标文件**: `app/miele/lib/func.php`
- **新增方法**: `public static function isB1Product($materialId)`

### 完整实现代码

```php
/**
 * 判断商品是否为B1类商品
 * 
 * 通过查询sales_material表获取class_id，再查询customer_classify表
 * 判断class_name是否为'B1'来确定商品分类
 * 
 * @param string $materialId 物料ID (sales_material_bn)
 * @return bool true表示B1商品，false表示非B1商品或查询失败
 * 
 * @example
 * if (miele_func::isB1Product('MATERIAL_001')) {
 *     // 处理B1商品逻辑
 * }
 */
public static function isB1Product($materialId) {
    // 参数验证
    if (empty($materialId) || !is_string($materialId)) {
        kernel::log('warning', 'B1Product_Check: Invalid materialId parameter', [
            'material_id' => $materialId,
            'type' => gettype($materialId)
        ]);
        return false;
    }
    
    try {
        // 记录开始时间用于性能监控
        $startTime = microtime(true);
        
        // 查询销售物料信息获取class_id
        $salesMaterialMdl = app::get('material')->model('sales_material');
        $salesMaterial = $salesMaterialMdl->db_dump([
            'sales_material_bn' => $materialId
        ], 'class_id');
        
        if (empty($salesMaterial) || !isset($salesMaterial['class_id'])) {
            kernel::log('info', 'B1Product_Check: sales_material not found', [
                'material_id' => $materialId,
                'execution_time' => round((microtime(true) - $startTime) * 1000, 2) . 'ms'
            ]);
            return false;
        }
        
        $classId = $salesMaterial['class_id'];
        
        // 查询客户分类信息
        $classifyMdl = app::get('material')->model('customer_classify');
        $classify = $classifyMdl->db_dump([
            'class_id' => $classId
        ], 'class_name');
        
        if (empty($classify) || !isset($classify['class_name'])) {
            kernel::log('info', 'B1Product_Check: customer_classify not found', [
                'material_id' => $materialId,
                'class_id' => $classId,
                'execution_time' => round((microtime(true) - $startTime) * 1000, 2) . 'ms'
            ]);
            return false;
        }
        
        // 判断是否为B1类商品
        $isB1 = ($classify['class_name'] === 'B1');
        $executionTime = round((microtime(true) - $startTime) * 1000, 2);
        
        // 记录判断结果
        kernel::log('info', 'B1Product_Check: completed', [
            'material_id' => $materialId,
            'class_id' => $classId,
            'class_name' => $classify['class_name'],
            'is_b1' => $isB1,
            'execution_time' => $executionTime . 'ms'
        ]);
        
        // 性能监控：如果执行时间超过100ms记录警告
        if ($executionTime > 100) {
            kernel::log('warning', 'B1Product_Check: slow query detected', [
                'material_id' => $materialId,
                'execution_time' => $executionTime . 'ms'
            ]);
        }
        
        return $isB1;
        
    } catch (Exception $e) {
        // 异常处理：记录详细错误信息但不抛出异常
        kernel::log('error', 'B1Product_Check: exception occurred', [
            'material_id' => $materialId,
            'error_message' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]);
        return false;
    }
}
```

### 数据库查询优化

#### 查询性能考虑
1. **使用db_dump而非dump**: 提高查询性能
2. **只查询必要字段**: 减少数据传输量
3. **添加执行时间监控**: 便于性能调优

#### 查询语句分析
```sql
-- 第一次查询：获取class_id
SELECT class_id FROM sales_material WHERE sales_material_bn = ?

-- 第二次查询：获取class_name  
SELECT class_name FROM customer_classify WHERE class_id = ?
```

### 错误处理策略

#### 错误分类和处理
1. **参数错误**: 空值或类型错误，记录warning级别日志
2. **数据不存在**: 物料或分类不存在，记录info级别日志
3. **数据库异常**: 连接或查询异常，记录error级别日志
4. **性能异常**: 执行时间超过阈值，记录warning级别日志

#### 日志记录规范
- **日志前缀**: 统一使用 `B1Product_Check:` 便于日志过滤
- **关键信息**: 包含material_id、执行时间、错误详情
- **性能监控**: 记录每次查询的执行时间

### 集成点分析

#### 调用方式
```php
// 在订单处理中调用
if (miele_func::isB1Product($item['sales_material_bn'])) {
    // B1商品处理逻辑
}

// 在ESB同步中调用
$isB1 = miele_func::isB1Product($materialCode);
```

#### 依赖关系
- **依赖**: material应用的sales_material和customer_classify模型
- **被依赖**: 订单处理逻辑、ESB同步逻辑

### 测试用例设计

#### 正常情况测试
1. **B1商品测试**: 返回true
2. **非B1商品测试**: 返回false

#### 异常情况测试
1. **空参数测试**: 返回false
2. **无效参数测试**: 返回false
3. **不存在物料测试**: 返回false
4. **数据库异常测试**: 返回false

#### 性能测试
1. **响应时间测试**: 验证<100ms要求
2. **并发测试**: 验证多线程调用安全性

### 部署注意事项

#### 向后兼容性
- 新增方法不影响现有功能
- 静态方法可以直接调用，无需实例化

#### 配置要求
- 确保material应用可用
- 确保数据库连接正常
- 确保相关表数据完整

#### 监控指标
- 方法调用次数
- 平均响应时间
- 错误率统计
- B1商品识别准确率

### 验收标准

#### 功能验收
- [ ] B1商品正确识别为true
- [ ] 非B1商品正确识别为false
- [ ] 异常情况正确处理并返回false
- [ ] 日志记录完整且格式正确

#### 性能验收
- [ ] 单次调用响应时间<100ms
- [ ] 并发调用无异常
- [ ] 内存使用无泄漏

#### 代码质量验收
- [ ] 代码符合项目编码规范
- [ ] 注释完整且准确
- [ ] 错误处理完善
- [ ] 日志记录规范

---

## 实施步骤

### Step 1: 代码实现
1. 在 `app/miele/lib/func.php` 中添加 `isB1Product` 方法
2. 确保代码格式符合项目规范
3. 添加完整的注释和文档

### Step 2: 单元测试
1. 创建测试文件 `tests/unit/miele/lib/FuncTest.php`
2. 实现所有测试用例
3. 确保测试覆盖率达到90%以上

### Step 3: 集成验证
1. 在开发环境部署代码
2. 使用真实数据进行验证
3. 检查日志记录是否正常

### Step 4: 性能测试
1. 进行响应时间测试
2. 进行并发测试
3. 优化查询性能（如需要）

### Step 5: 代码审查
1. 提交代码审查
2. 根据反馈修改代码
3. 确保代码质量达标

---

## 相关文档
- **需求文档**: [requirements.md](requirements.md) - Requirement 1
- **设计文档**: [design.md](design.md) - Component 1
- **数据库文档**: `docs/cheatsheet/database/query-basic.md`
- **类加载规则**: `docs/cheatsheet/system/class-loading-rules.md`
