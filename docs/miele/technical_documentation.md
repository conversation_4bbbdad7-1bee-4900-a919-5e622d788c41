# Miele App - 技术文档

## 1. 概述

`miele` 应用是 OMS 系统中的一个核心模块，负责处理美诺（Miele）品牌的特定业务逻辑。它遵循模型-视图-控制器（MVC）架构，并通过基于服务的方式与其他 OMS 模块紧密集成。

## 2. 架构

该应用的架构基于传统的 MVC 模式，具有清晰的关注点分离：

- **模型（Model）:** 代表数据结构和业务逻辑。模型位于 `app/miele/model/` 目录下，并且似乎继承自父类 `dbeav_model`（在 `app.xml` 中定义），这表明存在一个用于数据库交互的通用基础层。
- **视图（View）:** 表示层，可能包含用户界面的模板。文件位于 `app/miele/view/` 目录下。
- **控制器（Controller）:** 处理用户请求，与模型交互，并渲染视图。控制器位于 `app/miele/controller/` 目录下。

### 配置驱动的方法

一个关键的架构特性是广泛使用 XML 文件进行配置和服务注册：

- **`app.xml`**: 定义应用的元数据，如名称、版本、作者和依赖项。它还指定了父模型类，指向一个共享的基础模型（`dbeav_model`）。
- **`desktop.xml`**: 配置用户界面，包括菜单、工作区和权限。它将 UI 元素映射到特定的控制器操作，从而有效地定义了面向用户的路由和访问控制。
- **`services.xml`**: 通过注册服务类来实现面向服务的架构，这些服务类侦听事件或提供特定功能。这使得 `miele` 模块能够扩展或修改其他模块（如 `ome`、`erpapi`、`monitor`）的行为，而无需更改其核心代码。

## 3. 关键组件

### 3.1. 目录结构

- **`controller/`**: 包含处理 HTTP 请求的控制器类。
- **`model/`**: 包含用于与数据库交互的数据模型。
- **`view/`**: 包含表示文件（模板）。
- **`dbschema/`**: 包含定义模块表数据库模式的 XML 文件。
- **`lib/`**: 包含不属于特定模型的库类和业务逻辑。
- **`initial/`**: 可能包含模块的初始数据或设置脚本。
- **`docs/`**: 包含模块的文档。

### 3.2. 服务层

`services.xml` 文件表明了一个解耦的、事件驱动的架构。`miele` 模块注册自己的类来响应由其他应用程序触发的事件。

**服务集成示例:**

- **订单处理:** 在 `ome` 或 `erpapi` 中创建订单后，会触发 `miele_service_order_create` 类，从而允许应用美诺特定的逻辑。
- **退款和退货:** 在退货和退款事件上会触发 `miele_service_reship_create` 和 `miele_service_refund_apply_refundonly`。
- **UI Finders:** 像 `desktop_finder.miele_mdl_sales_material_setmapping` 这样的服务为管理界面提供数据查找器，从而为美诺特定的数据启用自定义搜索和显示逻辑。

## 4. 数据模型和数据库

数据库模式在 `app/miele/dbschema/` 目录中定义。此目录中的每个 XML 文件对应一个数据库表。父模型类 `dbeav_model` 表明使用了由 `dbeav` 应用程序提供的自定义 ORM 或数据库抽象层。

## 5. 外部依赖

`miele` 模块依赖于 OMS 中的其他几个应用程序，如 `services.xml` 中的服务注册所示：

- **`ome`**: 核心订单管理引擎。
- **`erpapi`**: ERP 集成 API。
- **`monitor`**: 监控和事件系统。
- **`openapi`**: OpenAPI 接口。
- **`dbeav`**: 基础数据库抽象层。