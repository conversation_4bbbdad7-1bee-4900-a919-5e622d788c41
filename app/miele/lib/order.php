<?php
/**
 * 订单业务处理Lib方法类
 *
 * <AUTHOR>
 * @version 2025.06.11
 */
class miele_order extends miele_abstract
{

    /**
     * 判断是否大家电
     */
    public function isLargeAppliance($orderId)
    {
        $billLabelMdl = app::get('ome')->model('bill_label');
        $labelsMdl = app::get('omeauto')->model('order_labels');
        //bill_label
        $fitler = [
            'bill_type' => 'order',
            'bill_id' => $orderId,
        ];
        $orderLabelList = $billLabelMdl->getList('id,bill_id,label_id', $fitler);
        if (empty($orderLabelList)) {
            return false;
        }
        
        $labelIds = array_column($orderLabelList, 'label_id');
        $labelList = $labelsMdl->getList('label_id,label_code,label_name', ['label_id'=>$labelIds]);
        if (empty($labelList)) {
            return false;
        }
        
        $labelList = array_column($labelList, 'label_code');
        if (in_array($this->large_appliance, $labelList)) {
            return true;
        }
        
        return false;
    }

    /**
     * 订单业务处理：根据大家电、小家电，预处理相关业务；
     *
     * @param $orderInfo
     * @return void
     */
    public function updateOrderProfession($orderInfo)
    {
        //order_id
        $order_id = $orderInfo['order_id'];
        $isLargeAppliance = $this->isLargeAppliance($order_id);
        
        //type
        if($isLargeAppliance){
            // 大家电
            $result = $this->largeApplianceOrder($orderInfo);
        }else{
            // 小家电
            //$result = $this->smallApplianceOrder($orderInfo);
        }
        
        return $this->succ('订单业务已处理');
    }
    
    /**
     * [大家电]订单业务处理
     * @todo：订单屏蔽发货，更新order_object明细为：is_sh_ship='N';
     *
     * @param $orderInfo
     * @return array
     */
    public function largeApplianceOrder($orderInfo)
    {
        $orderMdl = app::get('ome')->model('orders');
        $orderObjObj = app::get('ome')->model('order_objects');
        $operLogMdl = app::get('ome')->model('operation_log');
        
        // order_id
        $order_id = $orderInfo['order_id'];
        
        // update order_objects
        $orderObjObj->update(['is_sh_ship'=>'true'], ['order_id'=>$order_id]);
        
        // 是否允许审核
        if(isset($orderInfo['is_not_combine']) && $orderInfo['is_not_combine'] > 0){
            // [按位或运算符]添加大家电订单标识
            $is_not_combine = $orderInfo['is_not_combine'] | ome_order_combine_const::__LARGE_APPLIANCES;
        }else{
            $is_not_combine = ome_order_combine_const::__LARGE_APPLIANCES;
        }
        
        $orderMdl->update(['is_not_combine'=>$is_not_combine], ['order_id'=>$order_id]);
        
        // logs
        $log_msg = '【大家电】订单明细更新为：屏蔽发货，并且不允许审核订单';
        $operLogMdl->write_log('order_modify@ome', $order_id, $log_msg);
        
        return $this->succ('订单屏蔽发货成功');
    }
    
    /**
     * [小家电]订单业务处理
     *
     * @param $orderInfo
     * @return array
     */
    public function smallApplianceOrder($orderInfo)
    {
        $orderMdl = app::get('ome')->model('orders');
        $orderObjObj = app::get('ome')->model('order_objects');
        $operLogMdl = app::get('ome')->model('operation_log');
        
        //order_id
        $order_id = $orderInfo['order_id'];
        
        //下面几步现在已经不用实现：
        //1、小家电预约信息取接口推送时的当前时间
        
        //2、更新 sdb_ome_orders.is_reserved = '1';
        
        //3、更新sdb_ome_order_items 扩展reserved_time字段
        
        return $this->succ('订单屏蔽发货成功');
    }
    
    /**
     * OMS创建订单后,调用SAP相关业务
     *
     * @param $orderInfo
     * @return string
     */
    public function create_order($orderInfo)
    {
        //check
        if (empty($orderInfo) || !isset($orderInfo['order_id'])) {
            $error_msg = '无效的订单数据。';
            return $this->error($error_msg);
        }

        $orderInfo = app::get('ome')->model('orders')->dump(['order_id'=>$orderInfo['order_id']], '*', array('order_objects'=>array('*',array('order_items'=>array('*')))));

        $db = kernel::database();
        $db->beginTransaction();

        $soData = $this->_formatOrderSapSo($orderInfo);
        $soId = app::get('miele')->model('sap_so')->insert($soData);
        if (!$soId) {
            $db->rollBack();
            return $this->error('创建SO单失败');
        }

        $itemsData = $this->_formatOrderSapSoItems($soId, $orderInfo);
        if (empty($itemsData)) {
            $db->rollBack();
            return $this->error('创建SO单明细失败,明细为空');
        }

        foreach ($itemsData as $item) {
            $rs = app::get('miele')->model('sap_so_items')->insert($item);
            if (!$rs) {
                $db->rollBack();
                return $this->error('创建SO单明细失败');
            }
        }

        // 新增：B1商品仓库分配逻辑
        $this->assignWarehouseForB1Products(['order_id' => $orderInfo['order_id'], 'so_id' => $soId]);

        $db->commit();

        // 更新预约状态
        $this->updateReservationStatus($orderInfo['order_id']);

        return $this->succ('SAP创建订单成功');
    }

    /**
     * 更新订单信息
     */
    public function update_order($order_id)
    {
        $soInfo = app::get('miele')->model('sap_so')->dump(['order_id'=>$order_id], '*');
        if (empty($soInfo)) {
            // 放入到队列，后续再更新 
            return $this->error('SO单不存在');
        }

        $db = kernel::database();
        $db->beginTransaction();
        try {
            // 更新主单信息
            $this->_updateOrder($soInfo, $order_id);

            // 更新明细
            $this->_updateOrderItems($soInfo, $order_id);

            // 更新发货相关信息
            $this->_updateOrderDelilvery($soInfo, $order_id);

            // 新增：B1商品仓库分配逻辑
            $this->assignWarehouseForB1Products(['order_id' => $order_id, 'so_id' => $soInfo['id']]);

            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            return $this->error('更新SO单失败');
        }

        // 更新预约状态
        $this->updateReservationStatus($order_id);
        
        return $this->succ('更新SO单成功');
    }

    private function _updateOrderDelilvery($soInfo, $order_id) {
        // 获取有效的发货单
        $deliveryId = app::get('ome')->model('delivery')->getDeliverIdByOrderId($order_id);
        if (empty($deliveryId)) {
            return true;
        }
        
        $isDelivery = false;
        $soItems = app::get('miele')->model('sap_so_items')->getList(['so_id'=>$soInfo['so_id']], '*');
        foreach ($soItems as $soItem) {
            $deliveryDetail = app::get('ome')->model('delivery_items_detail')->dump(['delivery_id'=>$deliveryId, 'item_id'=>$soItem['order_item_id']], '*');
            if (empty($deliveryDetail)) {
                continue;
            }

            $itemDeliveryId = $deliveryDetail['delivery_id'];
            $deliveryInfo = app::get('ome')->model('delivery')->dump(['delivery_id'=>$itemDeliveryId], '*');

            $branchInfo = app::get('ome')->model('branch')->dump(['branch_id'=>$deliveryInfo['branch_id']], '*');

            $updateData = [
                'branch_id' => $deliveryInfo['branch_id'],
                'branch_bn' => $branchInfo['branch_bn'],
                'up_time' => time(),
            ];
            $isDelivery = true;
            app::get('miele')->model('sap_so_items')->update($updateData, ['id'=>$soItem['id']]);
        }

        // 如果有发货单，并且是未同步过，则更新为待同步
        if ($isDelivery && $soInfo['sap_sync_status'] == 'none') {

            // 补差订单，不需要改同步状态
            if ($soInfo['order_type'] == 'BUCHA') {
                return true;
            }

            $updateData = [
                'sap_sync_status' => 'pending',
                'up_time' => time(),
            ];
            app::get('miele')->model('sap_so')->update($updateData, ['so_id'=>$soInfo['so_id']]);
        }

        return true;
    }

    private function _updateOrder($soInfo, $order_id) {
        $orderInfo = app::get('ome')->model('orders')->dump(['order_id'=>$order_id], '*');
        $soData = $this->_formatOrderSapSo($orderInfo);
        unset($soData['sap_sync_status']);
        app::get('miele')->model('sap_so')->update($soData, ['so_id'=>$soInfo['so_id']]);
        return true;
    }

    private function _updateOrderItems($soInfo, $order_id) {
        $itemList = app::get('ome')->model('order_items')->getList(['order_id'=>$order_id], '*');
        foreach ($itemList as $item) {
            $itemInfo = app::get('miele')->model('sap_so_items')->dump(['item_id'=>$item['order_item_id']], '*');
            $objects = app::get('ome')->model('order_objects')->dump(['obj_id'=>$item['obj_id']], '*');
            if (empty($itemInfo)) {
                $itemsData = [
                    'so_id' => $soInfo['so_id'],
                    'order_item_id' => $item['item_id'],
                    'oid' => $item['oid'],
                    'product_id' => $item['product_id'],
                    'sales_material_bn' => $objects['bn'],
                    'bn' => $item['bn'],
                    'name' => $item['name'],
                    'nums' => isset($item['quantity']) ? $item['quantity'] : $item['nums'],
                    'split_num' => $item['split_num'],
                    'sendnum' => $item['sendnum'],
                    'divide_order_fee' => $item['divide_order_fee'],
                    'actually_amount' => '0', // 客户实付
                    'platform_amount' => '0', // 平台实付
                    'pmt_price' => $item['pmt_price'], // 商品优惠
                    'settlement_amount' => $item['settlement_amount'], // 商家应收
                    'is_del' =>  $item['delete'], // 是否删除
                    'pay_status' => $objects['pay_status'], // 支付状态
                    'branch_id' => '0',
                    'branch_bn' => '',
                    'up_time' => time(),
                    'at_time' => time(),
                ];
                app::get('miele')->model('sap_so_items')->insert($itemsData);
            } else {
                $updateData = [
                    'so_id' => $soInfo['so_id'],
                    'is_del' => $item['delete'],
                    'pay_status' => $objects['pay_status'],
                    'up_time' => time(),
                ];
                app::get('miele')->model('sap_so_items')->update($updateData, ['id'=>$itemInfo['id']]);
            }
        }
    }

    private function _formatOrderSapSo($orderInfo)
    {
        $soData = array(
            'order_id' => $orderInfo['order_id'],
            'order_bn' => $orderInfo['order_bn'],
            'order_type' => $orderInfo['order_type'],
            'source' => 'order', // 默认来源为订单
            'order_status' => $orderInfo['status'],
            'bill_label' => $this->_getBillLabel($orderInfo['order_id']),
            'platform_order_bn' => $orderInfo['platform_order_bn'],
            'createtime' => $orderInfo['createtime'],
            'paytime' => $orderInfo['paytime'],
            'is_invoice' => $orderInfo['is_tax'] == 'true' ? '1' : '0',
            'ship_area' => $orderInfo['consignee']['area'],
            'ship_addr' => $orderInfo['consignee']['addr'],
            'ship_mobile' => $orderInfo['consignee']['mobile'],
            'ship_name' => $orderInfo['consignee']['name'],
            'seller_code' => $orderInfo['seller_code'],
            'order_memo' => $orderInfo['custom_mark'],
            'shop_id' => $orderInfo['shop_id'],
            'shop_type' => $orderInfo['shop_type'],
            'total_amount' => $orderInfo['total_amount'],
            'sap_sync_status' => 'none',
            'custom_reserved' => '0',
        );

        $shopInfo = app::get("ome")->model("shop")->dump(array('shop_id'=>$orderInfo['shop_id']));
        $soData['shop_bn'] = $shopInfo['shop_bn']; 

        $invoiceInfo = $this->_getInvoiceInfo($orderInfo['order_id']);
        if ($invoiceInfo) {
            $soData['invoice_title'] = $invoiceInfo['tax_title'];
            $soData['invoice_kind'] = $invoiceInfo['invoice_kind'];
        }  

        return $soData;
    }

    public function _formatOrderSapSoItems($soId, $orderInfo) {
        $itemsData = [];
        foreach ($orderInfo['order_objects'] as $objects) {
            foreach ($objects['order_items'] as $item) {
                $itemsData[] = [
                    'so_id' => $soId,
                    'order_item_id' => $item['item_id'],
                    'oid' => $objects['oid'],
                    'product_id' => $item['product_id'],
                    'sales_material_bn' => $objects['bn'],
                    'bn' => $item['bn'],
                    'name' => $item['name'],
                    'nums' => isset($item['nums']) ? $item['nums'] : $item['quantity'],
                    'split_num' => $item['split_num'],
                    'sendnum' => $item['sendnum'],
                    'divide_order_fee' => $item['divide_order_fee'],
                    'actually_amount' => '0', // 客户实付
                    'platform_amount' => '0', // 平台实付
                    'pmt_price' => $item['pmt_price'], // 商品优惠
                    'settlement_amount' => $item['settlement_amount'], // 商家应收
                    'is_del' =>  $item['delete'], // 是否删除
                    'pay_status' => $objects['pay_status'], // 支付状态
                    'branch_id' => '0',
                    'branch_bn' => '',
                    'up_time' => time(),
                    'at_time' => time(),
                ];
            }
        }
        return $itemsData;
    }

    private function _getInvoiceInfo($orderId)
    {
        $invoiceInfo = app::get('ome')->model('order_invoice')->dump(['order_id'=>$orderId], '*');
        if(empty($invoiceInfo)){
            return '';
        }
        return $invoiceInfo;
    }

    private function _getBillLabel($orderId)
    {
        $billLabelMdl = app::get('ome')->model('bill_label');
        $billLabelInfo = $billLabelMdl->dump(['bill_id'=>$orderId, 'bill_type'=>'order_type'], 'label_code');
        if(empty($billLabelInfo)){
            return '';
        }
        return $billLabelInfo['label_code'];
    }
    
    /**
     * 获取平台订单原始信息
     *
     * @param $orderId
     * @return string
     */
    public function getPlatformOrderInfo($order_id)
    {
        $orderMdl = app::get('ome')->model('orders');
        
        // 获取订单信息
        $orderInfo = $orderMdl->dump(['order_id'=>$order_id], '*', array('order_objects'=>array('*',array('order_items'=>array('*')))));
        if(empty($orderInfo)){
            return [];
        }
        
        // 获取平台商品信息
        $platform_objects = [];
        foreach ($orderInfo['order_objects'] as $objKey => $objVal)
        {
            $goods_id = $objVal['goods_id'];
            
            $platform_objects[$goods_id] = $objVal;
            
            // check
            if(empty($objVal['addon'])){
                continue;
            }
            
            // addon
            $addon = json_decode($objVal['addon'], true);
            
            // platform_outer_iid
            if($addon['platform_outer_iid']){
                $platform_objects[$goods_id]['platform_outer_iid'] = $addon['platform_outer_iid'];
            }
        }
        $orderInfo['platform_objects'] = $platform_objects;
        
        return $orderInfo;
    }
    
    public function formatOrderItemList($item_list, $platformOrderInfo)
    {
        // check 原样返回数据
        if(empty($item_list) || empty($platformOrderInfo)){
            return $item_list;
        }
        
        if(!isset($platformOrderInfo['platform_objects'])){
            return $item_list;
        }
        
        // format
        foreach ($item_list as $itemKey => $items)
        {
            foreach ($items as $key => $item)
            {
                $goods_id = $item['goods_id'];
                
                if(!isset($platformOrderInfo['platform_objects'][$goods_id])){
                    continue;
                }
                
                // platform_outer_iid
                $item_list[$itemKey][$key]['platform_outer_iid'] = $platformOrderInfo['platform_objects'][$goods_id]['platform_outer_iid'];
            }
        }
        
        return $item_list;
    }

    /**
     * 根据预约状态更新SO单和SO明细的预约字段
     *
     * @param $order_id int 订单ID
     * @return array
     */
    public function updateReservationStatus($order_id)
    {
        // 获取订单预约信息
        $reservationMdl = app::get('ome')->model('order_reservation');
        $reservationInfo = $reservationMdl->dump(['order_id'=>$order_id], '*');
        
        if(empty($reservationInfo)){
            return $this->succ('订单无预约信息');
        }
        
        // 获取SO单信息
        $soMdl = app::get('miele')->model('sap_so');
        $soInfo = $soMdl->dump(['order_id'=>$order_id], '*');
        
        if(empty($soInfo)){
            return $this->succ('SO单不存在，跳过预约状态更新');
        }
        
        // 根据预约状态更新SO单
        $soUpdateData = [];
        
        // 预约状态映射：0待预约->0未预约，1已预约->1已预约，2预约取消->0未预约
        if($reservationInfo['custom_reserved_status'] == '1'){
            $soUpdateData['custom_reserved'] = '1'; // 已预约
        }else{
            $soUpdateData['custom_reserved'] = '0'; // 未预约
        }
        
        // 预约时间处理
        if($reservationInfo['custom_reserved_status'] == '1'){
            // 已预约状态：设置预约时间
            if(!empty($reservationInfo['custom_reserved_time'])){
                $soUpdateData['reserved_time'] = $reservationInfo['custom_reserved_time'];
            }
        }else{
            // 未预约或预约取消状态：将预约时间设置为空
            $soUpdateData['reserved_time'] = null;
        }
        
        // 更新SO单
        if(!empty($soUpdateData)){
            $soUpdateData['up_time'] = time();
            $soMdl->update($soUpdateData, ['so_id'=>$soInfo['so_id']]);
        }
        
        // 更新同步状态：大家电预约成功后更新为pending
        if($reservationInfo['custom_reserved_status'] == '1' && $this->isLargeAppliance($order_id)){
            // 大家电预约成功，且当前状态为none时，更新为pending
            if($soInfo['sap_sync_status'] == 'none' && $soInfo['order_type'] != 'BUCHA'){
                $syncUpdateData = [
                    'sap_sync_status' => 'pending',
                    'up_time' => time(),
                ];
                $soMdl->update($syncUpdateData, ['so_id'=>$soInfo['so_id']]);
                
                // 记录同步状态更新日志
                $operLogMdl = app::get('ome')->model('operation_log');
                $syncLogMsg = '【大家电】预约成功，SO单同步状态更新为：待同步';
                $operLogMdl->write_log('order_modify@ome', $order_id, $syncLogMsg);
            }
        }
        
        // 更新SO明细的预约时间
        $soItemsMdl = app::get('miele')->model('sap_so_items');
        $soItems = $soItemsMdl->getList(['so_id'=>$soInfo['so_id']], '*');
        
        if(!empty($soItems)){
            $itemUpdateData = [
                'up_time' => time(),
            ];
            
            // 根据预约状态设置预约时间
            if($reservationInfo['custom_reserved_status'] == '1'){
                // 已预约状态：设置预约时间
                if(!empty($reservationInfo['custom_reserved_time'])){
                    $itemUpdateData['reserved_time'] = $reservationInfo['custom_reserved_time'];
                }
            }else{
                // 未预约或预约取消状态：将预约时间设置为空
                $itemUpdateData['reserved_time'] = null;
            }
            
            foreach($soItems as $item){
                $soItemsMdl->update($itemUpdateData, ['id'=>$item['id']]);
            }
        }
        
        // 记录操作日志
        $operLogMdl = app::get('ome')->model('operation_log');
        $reservedTimeStr = '无';
        if($reservationInfo['custom_reserved_status'] == '1' && !empty($reservationInfo['custom_reserved_time'])){
            $reservedTimeStr = date('Y-m-d H:i:s', $reservationInfo['custom_reserved_time']);
        }elseif($reservationInfo['custom_reserved_status'] != '1'){
            $reservedTimeStr = '已清空';
        }
        
        $log_msg = sprintf('客户预约状态：%s，预约时间：%s', 
            $soUpdateData['custom_reserved'] == '1' ? '已预约' : '未预约',
            $reservedTimeStr
        );
        $operLogMdl->write_log('order_modify@ome', $order_id, $log_msg);
        
        return $this->succ('预约状态更新成功');
    }

    /**
     * 为B1商品分配默认仓库
     * @param array $orderInfo 订单信息
     */
    private function assignWarehouseForB1Products($orderInfo) {
        if (empty($orderInfo['order_id']) || empty($orderInfo['so_id'])) {
            return;
        }

        $orderId = $orderInfo['order_id'];
        $soId = $orderInfo['so_id'];

        // 获取订单商品信息
        $soItemsMdl = app::get('miele')->model('sap_so_items');
        $soItems = $soItemsMdl->getList('*', ['so_id' => $soId]);

        if (empty($soItems)) {
            return;
        }

        // 遍历处理每个商品
        foreach ($soItems as $item) {
            if (empty($item['sales_material_bn']) || !empty($item['branch_id'])) {
                continue;
            }

            // 判断是否为B1商品
            if (miele_func::isB1Product($item['sales_material_bn'])) {
                // 获取默认仓库
                $branchId = miele_func::getDefaultBranch($orderId);

                if ($branchId) {
                    // 更新仓库信息
                    $soItemsMdl->update(['branch_id' => $branchId], ['id' => $item['id']]);
                }
            }
        }
    }
}