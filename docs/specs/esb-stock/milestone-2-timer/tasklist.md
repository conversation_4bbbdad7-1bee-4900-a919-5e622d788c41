---
# 里程碑2: 定时任务调整
# 状态: 待处理
# 源任务: docs/specs/SAP库存同步重构/tasks.md 中的里程碑2
---

> **核心原则**: 本文档是 AI 的精确执行手册。每个任务都应明确、原子化，并包含清晰的 `执行` 和 `验证` 步骤，确保操作的准确性和可追溯性。

---

- [ ] **任务 2.1: 验证定时任务当前实现** `[待处理]`
  - **目标文件**: `app/miele/lib/autotask/timer/stocksync.php`
  - **操作**: `检查`
  - **执行**:
    - **查看当前process方法实现**:
      ```shell
      grep -A 30 "public function process" app/miele/lib/autotask/timer/stocksync.php
      ```
    - **确认当前同步流程**:
      ```shell
      grep -B 5 -A 10 "syncStock" app/miele/lib/autotask/timer/stocksync.php
      ```
  - **验证**:
    ```shell
    php -l app/miele/lib/autotask/timer/stocksync.php && echo "语法检查通过"
    ```

---

- [ ] **任务 2.2: 设计定时任务重构方案** `[待处理]`
  - **目标文件**: `app/miele/lib/autotask/timer/stocksync.php`
  - **操作**: `分析`
  - **执行**:
    - **分析当前流程**:
      ```shell
      grep -n "generateAdjustOrders\|adjustService" app/miele/lib/autotask/timer/stocksync.php
      ```
    - **确认需要的重构点**:
      - 在syncStock成功后自动调用generateAdjustOrders
      - 添加并发控制机制
      - 实现分批处理逻辑（每批100个物料）
  - **验证**:
    ```shell
    echo "分析完成，确认重构需求"
    ```

---

- [ ] **任务 2.3: 验证分批处理需求** `[待处理]`
  - **目标文件**: `app/miele/lib/autotask/timer/stocksync.php`
  - **操作**: `检查`
  - **执行**:
    - **查看物料获取逻辑**:
      ```shell
      grep -B 5 -A 10 "getList.*material_bn" app/miele/lib/autotask/timer/stocksync.php
      ```
    - **确认是否已有分批处理**:
      ```shell
      grep "array_chunk\|batch" app/miele/lib/autotask/timer/stocksync.php
      ```
  - **验证**:
    ```shell
    echo "分批处理需求确认完成"
    ```

---

- [ ] **任务 2.4: 验证并发控制需求** `[待处理]`
  - **目标文件**: `app/miele/lib/autotask/timer/stocksync.php`
  - **操作**: `检查`
  - **执行**:
    - **查看是否已有锁机制**:
      ```shell
      grep -i "lock\|acquire\|redis" app/miele/lib/autotask/timer/stocksync.php
      ```
    - **确认并发控制需求**:
      ```shell
      echo "需要添加Redis分布式锁，防止重复执行"
      ```
  - **验证**:
    ```shell
    echo "并发控制需求确认完成"
    ```

---
