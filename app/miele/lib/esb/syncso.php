<?php

class miele_esb_syncso extends miele_esb_service
{
    private $method = 'miele.qimen.esb.so.sync';

    public function pushSo($soId)
    {
        $soMdl = app::get('miele')->model('sap_so');
        $so = $soMdl->db_dump(['so_id' => $soId, 'sap_sync_status' => array('pending','fail'), 'order_type|noequal' => 'BUCHA'], '*');
        if (empty($so)) {
            return array('res' => 'fail', 'message' => 'SO单不存在');
        }

        try {
            $soData = $this->_formatOrderSapSo($so);
        } catch (Exception $e) {
            return array('res' => 'fail', 'message' => $e->getMessage());
        }

        $soData['item'] = $this->_getSoItems($so);

        $this->original_bn = $so['order_bn'];
        $res = $this->qimenRequest($this->method, $soData);

        $opObj  = app::get('ome')->model('operation_log');
        $opObj->write_log('sap_so@miele', $so['id'], 'SO单同步返回:'. ($res['rsp'] == 'succ' ? '成功' : '失败').'，'.(htmlspecialchars($res['msg'] ?? $res['error_msg'])));

        if ($res['rsp'] == 'succ') {
            $updateData = [
                'sap_sync_status' => 'running',
                'sap_sync_time' => time(),
            ];
            app::get('miele')->model('sap_so')->update($updateData, ['id'=>$so['id']]);
            return array('res' => 'succ', 'message' => 'SO单请求成功');
        } else {
            return array('res' => 'fail', 'message' => $res['msg'] ?? 'SO单请求失败');
        }
    }

    private function _formatOrderSapSo($so)
    {
        if (!in_array($so['sap_sync_status'], ['pending', 'fail'])) {
            throw new Exception('同步状态为：' . $so['sap_sync_status'] . '，不能同步');
        }

        // 补差订单不推送SO单（source=order && order_type=BUCHA）
        if ($so['source'] == 'order' && $so['order_type'] == 'BUCHA') {
            throw new Exception('补差订单，不能同步');
        }

        $isLargeAppliance = kernel::single('miele_order')->isLargeAppliance($so['order_id']);

        if ($isLargeAppliance) {
            // 大家电，未预约，则不推
            if ($so['custom_reserved'] == '0') {
                throw new Exception('大家电，未预约，不能同步');
            }
            $processType = 'C1';
        } else {
            $processType = 'C2';
        }

        // 如果已经同步过，则进行更新
        if ($so['sap_so_bn']) {
            $processType = 'M';
        }

        $orderInfo = $this->_getOrderInfo($so);
        if ($orderInfo['status'] == 'cancel') {
            $processType = 'R';
        }

        $shopCustomerMappingMdl = app::get('miele')->model('shop_customer_mapping');
        $shopCustomerMapping = $shopCustomerMappingMdl->getList('*', ['shop_id' => $so['shop_id'], 'order_type' => $so['order_type']], 0, 1);
        if (empty($shopCustomerMapping)) {
            throw new Exception('店铺客户映射不存在，请先配置店铺客户映射');
        }
        $customer = $shopCustomerMapping[0]['customer_code'];

        $data = array(
            'otherSystemSO' => $so['order_bn'],
            'source' => 'O',
            'processType' => $processType,// C1大家电订单, C2小家电订单, M 修改订单, R作废
            'orderType' => $so['order_type'],
            'salesOrg' => '9600',
            'salesChannel' => '20',
            'division' => '23',
            'saleOffice' => '', // @TODO 门店编码，待定
            'customer' => $customer,
            'payer' => $customer, // customer 相同
            'billTo' => $customer, // customer 相同
            'shipTo' => $customer, // customer 相同
            'salesPerson' => $so['seller_code'],
            'currency' => 'CNY',
            'paymentTerm' => '0001',
            'completeDelivery' => in_array($orderInfo['process_status'], ['splited', 'remain_cancel']) ? 'Y' : 'N',
            'agName' =>  $orderInfo['consignee']['name'],
            'agAddr' =>  $isLargeAppliance ? $orderInfo['consignee']['addr'] : '', // 小家电不推地址
            'agCounty' =>  $orderInfo['consignee']['district'],
            'agCity' =>  $orderInfo['consignee']['city'],
            'agState' =>  $orderInfo['consignee']['province'],
            'agCountry' =>  '中国',
            'agMobile' =>  $isLargeAppliance ? $orderInfo['consignee']['mobile'] : '',
            'agEmail' =>  $orderInfo['consignee']['email'],
            'weName' =>  $orderInfo['consignee']['name'],
            'weAddr' =>  $isLargeAppliance ? $orderInfo['consignee']['addr'] : '',
            'weCounty' =>  $orderInfo['consignee']['district'],
            'weCity' =>  $orderInfo['consignee']['city'],
            'weState' =>  $orderInfo['consignee']['province'],
            'weCountry' =>  '中国',
            'weMobile' =>  $isLargeAppliance ? $orderInfo['consignee']['mobile'] : '',
            'weEmail' =>  $orderInfo['consignee']['email'],
        );

        if ($processType == 'M' || $processType == 'R') {
            $data['soS4'] = $so['sap_so_bn'];
        }

        if ($isLargeAppliance) {
            $data['deliveryDate'] = date('Ymd', $so['reserved_time']);
        } else {
            $data['deliveryDate'] = date('Ymd');
        }
        return $data;
    }

    private function _getSoItems($so)
    {
        $isLargeAppliance = kernel::single('miele_order')->isLargeAppliance($so['order_id']);

        $soItemsList = app::get('miele')->model('sap_so_items')->getList('*', ['so_id' => $so['id']]);
        $soItems = array();
        foreach ($soItemsList as $item) {
            // 小家电，没有仓库，则不推
            if (!$isLargeAppliance && empty($item['branch_id'])) {
                continue;
            }

            $itemData = [
                'otherSystemItem' => $item['id'],
                'higherLevel' => '', // 组合商品，则是销售物料编码
                'material' => $item['bn'],
                'unitPrice' => bcdiv($item['divide_order_fee'], $item['nums'], 2),
                'quantity' => $item['nums'],
                'plant' => '', // 工厂
                'shippingPoint' => '', // 用于区分专车/快递
                'storageLocation' => '', // 库位
            ];

            // 为大家电B1品设置plant和storageLocation
            if ($isLargeAppliance && miele_func::isB1Product($item['sales_material_bn']) && !empty($item['branch_id'])) {
                $funcObj = new miele_func();
                $branchParams = $funcObj->getBranchParams($item['branch_id']);
                if ($branchParams) {
                    $itemData['plant'] = $branchParams['plant'];
                    $itemData['storageLocation'] = $branchParams['storageLocation'];
                }
            }

            //@todo 如果是Set传 higherLevel，否则不需要传
            if ($item['bn'] != $item['sales_material_bn']) {
                $itemData['higherLevel'] = $item['sales_material_bn'];
            }

            if ($item['item_line_no']) {
                $itemData['itemS4'] = $item['item_line_no'];
            }

            if ($item['deliveryDate']) {
                $itemData['deliveryDate'] = date('Ymd', $item['reserved_time']);
            } else {
                if ($isLargeAppliance) {
                    //@TODO 如果推送时间比预约时间还晚，异常情况怎么处理？
                    $itemData['deliveryDate'] = date('Ymd', $so['reserved_time']);
                } else {
                    $itemData['deliveryDate'] = date('Ymd');
                }
            }

            if ($item['is_del'] == 'true') {
                $itemData['rejected'] = '00';
            }

            $soItems[] = $itemData;
        }
        return $soItems;
    }

    private function _getOrderInfo($so)
    {
        $orderInfo = app::get('ome')->model('orders')->dump(['order_id' => $so['order_id']], '*');
        $area = explode(":", $orderInfo['consignee']['area'])[1];
        $area = explode("/", $area);
        $orderInfo['consignee']['province'] = $area[0];
        $orderInfo['consignee']['city'] = $area[1];
        $orderInfo['consignee']['district'] = $area[2];
        $orderInfo['consignee']['town'] = $area[3];

        foreach ($orderInfo['consignee'] as $dk => $dv) {
            if (is_string($dv) && $index = strpos($dv, '>>')) {
                $orderInfo['consignee'][$dk] = substr($dv, 0, $index);
            }
        }

        return $orderInfo;
    }
}