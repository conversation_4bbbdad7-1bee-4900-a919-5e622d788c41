# Miele App - 业务场景

本文档概述了由 Miele 模块提供、在 `desktop.xml` 文件中配置的核心业务场景和功能。

## 1. 美诺管理 (`miele_manager`)

该工作区包含用于美诺特定数据的主要管理功能。

### 1.1. 物料管理

- **用户故事:** 作为一名后台用户，我需要管理 SET 产品的映射并查看来自 SAP 的主要产品主数据，以确保产品信息正确无误。
- **功能:**
    - 查看、添加、编辑和删除 SET 产品映射。
    - 导入和导出 SET 产品映射。
    - 查看 SAP 产品主数据 (`miele_goods`)。

### 1.2. 库存管理

- **用户故事:** 作为一名库存管理员，我需要管理库存同步的白名单并监控同步过程，以确保存货水平准确。
- **功能:**
    - 管理库存同步白名单 (`miele_stock_masterdata`)。
    - 查看库存同步记录 (`miele_stock_sync`)。

### 1.3. SO 单管理 (销售订单)

- **用户故事:** 作为一名订单处理员，我需要管理店铺和客户之间的映射，查看 SO 和 ODN 单据，并处理特殊的“补差”订单。
- **功能:**
    - 管理店铺到客户的映射。
    - 查看 SO 单据 (`miele_so`)。
    - 查看、同步和取消 ODN 单据 (`miele_odn_view`)。
    - 查看和替换补差订单 (`miele_bucha_order_view`)。

### 1.4. 售后管理

- **用户故事:** 作为一名客服代表，我需要管理退货单 (`reship`)。
- **功能:**
    - 查看和处理退货单。

### 1.5. 系统报表

- **用户故事:** 作为一名经理，我需要查看各种报告以了解当前的销售和退货状况。
- **功能:**
    - 生成以下报告:
        - 已发货订单。
        - 未发货订单。
        - 已取消订单。
        - 强制退款订单。
        - “7天无理由”退货 (已入库和未入库)。

## 2. 订单管理 (`order_center`)

该工作区将美诺特定的订单类型集成到主订单处理流程中。

### 2.1. 订单处理

- **用户故事:** 作为一名订单处理员，我需要处理一个需要手动确认的大家电订单的特殊队列。
- **功能:**
    - 查看和处理等待确认的“大家电”订单。