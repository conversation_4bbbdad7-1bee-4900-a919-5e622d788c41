重构库存同步方法，库存同步入口：syncStock

方法传入参数：基础物料编码，可以是多个

实现功能：
0 每次请求ESB接口都需要定义一个批次号，批次号不能和之前的重复，可以参考发货单号的生成
1 获取发货仓库列表，获取仓库列表规则请自行分析代码，仓库编码必须符合工厂编码:货位编码格式，否则跳过此仓库的同步
2 循环发货仓库列表，根据仓库编码和基础物料编码请求ESB接口获取库存，获取ESB接口已经有接口日志了，因此不需要操作日志
3 在获取ESB库存前，将sdb_miele_stock表中未同步的数据状态都改为不需要同步，已经同步成功的维持不变
4 将获取到的ESB接口返回的库存记录到sdb_miele_stock表中，用于后续将库存同步到OMS的库存表
5 根据sdb_miele_stock( app/miele/dbschema/stock.php)表中的记录将未同步到OMS的库存通过库存调整单更新到OMS库存表
6 syncStock 方法会有两个地方调用：
  6.1 定时任务调用可以新增一份方法，批量获取stock_masterdata 表中的基础物
料，分组调用syncStock，定时任务入口在：app/miele/lib/autotask/timer/stocksync.php 因此此。process方法也需要同步修改
  6.2 控制器中 miele_ctl_admin_stock_masterdata中的sync_stock方法也会调用，此方法里面只会有material_ids参数，因此需要通过material_ids获取基础物料编码进行分组调用，所以也需要将sync_stock方法进行重构