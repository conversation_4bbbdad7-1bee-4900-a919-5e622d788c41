# Task 3: 优化ESB同步逻辑支持大家电B1品仓库信息传递

## 任务概述
在 `app/miele/lib/esb/syncso.php` 中优化ESB同步逻辑，为大家电B1品添加仓库信息传递功能，确保下游系统能够获得必要的仓库数据。

## 详细设计

### 文件位置
- **目标文件**: `app/miele/lib/esb/syncso.php`
- **修改方法**: `_getSoItems($so)` 或相关的商品数据处理方法
- **新增私有方法**: `getBranchInfoForItem($item)`, `shouldIncludeWarehouseInfo($so, $item)`

### 核心实现逻辑

#### 1. 修改商品数据获取方法

```php
/**
 * 获取SO商品信息，为大家电B1品添加仓库信息
 * 
 * 在原有逻辑基础上，判断商品是否为大家电B1品
 * 如果是，则在同步数据中添加仓库信息
 * 
 * @param array $so SO单信息
 * @return array 包含仓库信息的商品列表
 */
private function _getSoItems($so) {
    // 原有逻辑获取商品列表...
    $items = []; // 这里是原有的商品数据获取逻辑
    // [保持原有代码不变]
    
    // 新增：为大家电B1品添加仓库信息
    try {
        kernel::log('info', 'ESB_Sync: processing warehouse info for items', [
            'order_bn' => $so['order_bn'] ?? 'unknown',
            'so_id' => $so['so_id'] ?? 'unknown',
            'item_count' => count($items)
        ]);
        
        $warehouseInfoAddedCount = 0;
        
        foreach ($items as &$item) {
            try {
                // 判断是否需要包含仓库信息
                if ($this->shouldIncludeWarehouseInfo($so, $item)) {
                    $branchInfo = $this->getBranchInfoForItem($item, $so);
                    
                    if ($branchInfo) {
                        // 添加仓库信息到商品数据中
                        $item['warehouse'] = $branchInfo;
                        $warehouseInfoAddedCount++;
                        
                        kernel::log('info', 'ESB_Sync: warehouse info added', [
                            'order_bn' => $so['order_bn'] ?? 'unknown',
                            'material_code' => $item['materialCode'] ?? $item['sales_material_bn'] ?? 'unknown',
                            'warehouse_info' => $branchInfo
                        ]);
                    } else {
                        kernel::log('warning', 'ESB_Sync: warehouse info not available', [
                            'order_bn' => $so['order_bn'] ?? 'unknown',
                            'material_code' => $item['materialCode'] ?? $item['sales_material_bn'] ?? 'unknown'
                        ]);
                    }
                }
                
            } catch (Exception $e) {
                // 单个商品处理失败不影响其他商品的同步
                kernel::log('error', 'ESB_Sync: warehouse info processing failed for item', [
                    'order_bn' => $so['order_bn'] ?? 'unknown',
                    'material_code' => $item['materialCode'] ?? $item['sales_material_bn'] ?? 'unknown',
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ]);
                // 继续处理其他商品
                continue;
            }
        }
        
        kernel::log('info', 'ESB_Sync: warehouse info processing completed', [
            'order_bn' => $so['order_bn'] ?? 'unknown',
            'total_items' => count($items),
            'warehouse_info_added' => $warehouseInfoAddedCount
        ]);
        
    } catch (Exception $e) {
        // 仓库信息处理失败不影响ESB同步主流程
        kernel::log('error', 'ESB_Sync: warehouse info processing failed', [
            'order_bn' => $so['order_bn'] ?? 'unknown',
            'error_message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        // 继续返回原有的商品数据，不中断同步流程
    }
    
    return $items;
}
```

#### 2. 实现仓库信息判断逻辑

```php
/**
 * 判断是否应该为商品包含仓库信息
 * 
 * 条件：商品是大家电 AND 商品是B1类
 * 
 * @param array $so SO单信息
 * @param array $item 商品信息
 * @return bool true表示需要包含仓库信息
 */
private function shouldIncludeWarehouseInfo($so, $item) {
    try {
        // 获取物料编码
        $materialCode = $item['materialCode'] ?? $item['sales_material_bn'] ?? '';
        
        if (empty($materialCode)) {
            kernel::log('warning', 'shouldIncludeWarehouseInfo: missing material code', [
                'order_bn' => $so['order_bn'] ?? 'unknown',
                'item_data' => $item
            ]);
            return false;
        }
        
        // 判断是否为B1商品
        $isB1Product = miele_func::isB1Product($materialCode);
        
        if (!$isB1Product) {
            return false;
        }
        
        // 判断是否为大家电
        $isLargeAppliance = $this->isLargeAppliance($so, $item);
        
        $shouldInclude = $isB1Product && $isLargeAppliance;
        
        kernel::log('debug', 'shouldIncludeWarehouseInfo: evaluation', [
            'order_bn' => $so['order_bn'] ?? 'unknown',
            'material_code' => $materialCode,
            'is_b1_product' => $isB1Product,
            'is_large_appliance' => $isLargeAppliance,
            'should_include' => $shouldInclude
        ]);
        
        return $shouldInclude;
        
    } catch (Exception $e) {
        kernel::log('error', 'shouldIncludeWarehouseInfo: exception occurred', [
            'order_bn' => $so['order_bn'] ?? 'unknown',
            'material_code' => $materialCode ?? 'unknown',
            'error_message' => $e->getMessage()
        ]);
        return false;
    }
}

/**
 * 判断商品是否为大家电
 * 
 * 调用现有的大家电判断逻辑
 * 如果现有逻辑不存在，需要根据业务规则实现
 * 
 * @param array $so SO单信息
 * @param array $item 商品信息
 * @return bool true表示是大家电
 */
private function isLargeAppliance($so, $item) {
    try {
        // 方案1：如果存在现有的大家电判断方法
        if (method_exists('miele_order', 'isLargeAppliance')) {
            $orderInstance = kernel::single('miele_order');
            return $orderInstance->isLargeAppliance($so['order_id'] ?? null);
        }
        
        // 方案2：根据商品属性判断（需要根据实际业务规则调整）
        $materialCode = $item['materialCode'] ?? $item['sales_material_bn'] ?? '';
        
        // 这里需要根据实际的大家电判断规则实现
        // 示例：通过商品编码前缀判断
        $largeAppliancePrefixes = ['DA', 'WM', 'RF', 'DW']; // 示例前缀
        
        foreach ($largeAppliancePrefixes as $prefix) {
            if (strpos($materialCode, $prefix) === 0) {
                return true;
            }
        }
        
        // 方案3：通过商品分类判断
        // 可以查询商品的其他分类信息来判断
        
        return false;
        
    } catch (Exception $e) {
        kernel::log('error', 'isLargeAppliance: exception occurred', [
            'order_bn' => $so['order_bn'] ?? 'unknown',
            'material_code' => $item['materialCode'] ?? $item['sales_material_bn'] ?? 'unknown',
            'error_message' => $e->getMessage()
        ]);
        return false;
    }
}
```

#### 3. 实现仓库信息获取方法

```php
/**
 * 获取商品的仓库信息
 * 
 * 从sap_so_items表获取仓库ID，然后格式化为ESB需要的格式
 * 
 * @param array $item 商品信息
 * @param array $so SO单信息
 * @return array|null 格式化的仓库信息，获取失败时返回null
 */
private function getBranchInfoForItem($item, $so) {
    try {
        $materialCode = $item['materialCode'] ?? $item['sales_material_bn'] ?? '';
        $soId = $so['so_id'] ?? null;
        
        if (empty($materialCode) || empty($soId)) {
            kernel::log('warning', 'getBranchInfoForItem: missing required parameters', [
                'material_code' => $materialCode,
                'so_id' => $soId
            ]);
            return null;
        }
        
        // 从sap_so_items表获取仓库ID
        $soItemsMdl = app::get('miele')->model('sap_so_items');
        $soItem = $soItemsMdl->db_dump([
            'so_id' => $soId,
            'sales_material_bn' => $materialCode
        ], 'branch_id');
        
        if (empty($soItem['branch_id'])) {
            kernel::log('info', 'getBranchInfoForItem: no warehouse assigned', [
                'material_code' => $materialCode,
                'so_id' => $soId
            ]);
            return null;
        }
        
        $branchId = $soItem['branch_id'];
        
        // 使用现有的getBranchParams方法格式化仓库信息
        $branchParams = miele_func::getBranchParams($branchId);
        
        if (empty($branchParams)) {
            kernel::log('warning', 'getBranchInfoForItem: failed to get branch params', [
                'material_code' => $materialCode,
                'branch_id' => $branchId
            ]);
            return null;
        }
        
        // 格式化为ESB需要的仓库信息格式
        $warehouseInfo = [
            'warehouse_id' => $branchId,
            'warehouse_code' => $branchParams['branch_bn'] ?? '',
            'warehouse_name' => $branchParams['branch_name'] ?? '',
            'storage_code' => $branchParams['storage_code'] ?? ''
        ];
        
        kernel::log('info', 'getBranchInfoForItem: warehouse info retrieved', [
            'material_code' => $materialCode,
            'branch_id' => $branchId,
            'warehouse_info' => $warehouseInfo
        ]);
        
        return $warehouseInfo;
        
    } catch (Exception $e) {
        kernel::log('error', 'getBranchInfoForItem: exception occurred', [
            'material_code' => $materialCode ?? 'unknown',
            'so_id' => $soId ?? 'unknown',
            'error_message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return null;
    }
}
```

### ESB数据格式设计

#### 原有商品数据格式
```json
{
    "materialCode": "MATERIAL_001",
    "quantity": 2,
    "price": 1000.00,
    "description": "商品描述"
}
```

#### 增强后的大家电B1品数据格式
```json
{
    "materialCode": "MATERIAL_001",
    "quantity": 2,
    "price": 1000.00,
    "description": "商品描述",
    "warehouse": {
        "warehouse_id": 123,
        "warehouse_code": "WH001",
        "warehouse_name": "主仓库",
        "storage_code": "ST001"
    }
}
```

### 向下兼容性保证

#### 1. 数据结构兼容
- 仓库信息作为新增字段，不影响现有字段
- 非大家电B1品保持原有数据结构
- ESB下游系统可选择性处理仓库信息

#### 2. 错误处理兼容
- 仓库信息获取失败不影响商品同步
- 保持原有的ESB同步成功率
- 异常情况下降级为原有逻辑

### 性能优化策略

#### 1. 批量查询优化
```php
// 优化：批量获取所有商品的仓库信息
private function getBatchBranchInfo($items, $soId) {
    $materialCodes = array_column($items, 'materialCode');
    
    $soItemsMdl = app::get('miele')->model('sap_so_items');
    $soItems = $soItemsMdl->getList('sales_material_bn,branch_id', [
        'so_id' => $soId,
        'sales_material_bn|in' => $materialCodes
    ]);
    
    // 构建material_code => branch_id的映射
    $branchMapping = [];
    foreach ($soItems as $item) {
        $branchMapping[$item['sales_material_bn']] = $item['branch_id'];
    }
    
    return $branchMapping;
}
```

#### 2. 缓存策略
- 考虑对getBranchParams结果进行缓存
- 减少重复的仓库信息查询
- 提高ESB同步性能

### 测试用例设计

#### 正常流程测试
1. **大家电B1品包含仓库信息**: 验证仓库信息正确添加
2. **非大家电B1品不包含**: 验证条件判断正确
3. **非B1商品不包含**: 验证B1判断逻辑
4. **普通商品保持原样**: 验证向下兼容性

#### 异常情况测试
1. **仓库信息缺失**: 验证优雅降级
2. **大家电判断异常**: 验证错误处理
3. **数据库查询失败**: 验证异常恢复
4. **格式化失败**: 验证数据完整性

#### 性能测试
1. **大订单同步**: 验证包含多个大家电B1品的订单
2. **并发同步**: 验证多个订单同时同步的性能
3. **响应时间**: 验证仓库信息处理不显著影响同步时间

### 验收标准

#### 功能验收
- [ ] 大家电B1品在ESB同步时包含仓库信息
- [ ] 非大家电B1品不包含仓库信息
- [ ] 仓库信息格式符合ESB接口规范
- [ ] 异常情况不影响ESB同步成功

#### 性能验收
- [ ] ESB同步时间增加<15%
- [ ] 仓库信息处理响应时间<100ms
- [ ] 并发同步无性能瓶颈

#### 质量验收
- [ ] 向下兼容，不影响现有ESB功能
- [ ] 错误处理完善，异常不中断同步
- [ ] 日志记录详细，便于问题排查
- [ ] 代码符合项目规范

---

## 实施步骤

### Step 1: 分析现有ESB同步逻辑
1. 研究 `app/miele/lib/esb/syncso.php` 的现有实现
2. 确定商品数据处理的具体方法
3. 了解ESB数据格式要求

### Step 2: 实现大家电判断逻辑
1. 确定现有的大家电判断方法
2. 如不存在，根据业务规则实现
3. 测试大家电判断的准确性

### Step 3: 实现仓库信息处理
1. 实现 `shouldIncludeWarehouseInfo` 方法
2. 实现 `getBranchInfoForItem` 方法
3. 集成到现有的商品数据处理流程

### Step 4: 测试和优化
1. 单元测试覆盖所有场景
2. 集成测试验证ESB同步完整性
3. 性能测试和优化

---

## 相关文档
- **需求文档**: [requirements.md](requirements.md) - Requirement 4
- **设计文档**: [design.md](design.md) - Component 3
- **Task 1**: [task-1.md](task-1.md) - 依赖的B1商品判断逻辑
