# Miele App - API 参考

本文档详细说明了 Miele 模块的 API 类集成。主要的集成方法是通过基于服务的、事件驱动的架构。

## 1. 内部 API / 事件钩子

`miele` 模块注册了多个服务类，这些服务类侦听由 OMS 内其他模块触发的事件。这些不是传统的 REST 或 RPC API，但它们是模块功能的关键集成点。

以下事件钩子在 `services.xml` 中注册：

| 服务 / 事件 ID                               | 类处理程序                            | 触发者 (模块) | 描述                                                                                             |
| ------------------------------------------------ | ---------------------------------------- | --------------------- | ------------------------------------------------------------------------------------------------------- |
| `operation_log`                                  | `miele_operation_log`                    | `miele` (自身)        | 记录 Miele 模块内的操作。                                                                |
| `erpapi.service.order.analysis.prepose`          | `miele_service_order_create`             | `erpapi`              | 在 ERP API 的订单分析之前触发，可能用于添加 Miele 特定的数据。                      |
| `ome.service.order.create.after`                 | `miele_service_order_create`             | `ome`                 | 在订单管理引擎 (OME) 中创建订单后触发。                                 |
| `ome.service.order.reservation`                  | `miele_service_order_reservation`        | `ome`                 | 处理订单预留，可能用于 Miele 特定的库存。                                          |
| `ome.service.reship.create.after`                | `miele_service_reship_create`            | `ome`                 | 在创建退货/换货订单后触发。                                                       |
| `ome.service.reship.check.before`                | `miele_service_reship_check`             | `ome`                 | 在检查退货单之前触发。                                                             |
| `ome.service.reship.check.after`                 | `miele_service_reship_check`             | `ome`                 | 在检查退货单之后触发。                                                              |
| `ome.service.refund.apply.refundonly.after`      | `miele_service_refund_apply_refundonly`  | `ome`                 | 在应用“仅退款”操作后触发。                                                      |
| `monitor.service.event.template.get.after`       | `miele_service_event_template_get`       | `monitor`             | 修改监控系统中的事件模板。                                                      |
| `erpapi.service.plugins.order.coupon.after`      | `miele_service_plugins_order_coupon`     | `erpapi`              | 通过 ERP API 处理 Miele 订单的优惠券逻辑。                                                  |
| `ome.service.order.change.after`                 | `miele_service_order_change`             | `ome`                 | 在订单更改后触发。                                                              |
| `ome.service.refund.apply.objectspaystatus.after`| `miele_service_order_update`             | `ome`                 | 在退款改变支付状态后，触发一个 Miele 特定的订单更新。                       |
| `ome.service.reship.finish.after`                | `miele_service_order_update`             | `ome`                 | 在退货完成后，触发一个 Miele 特定的订单更新。                                      |
| `ome.service.order.finishedit.after`             | `miele_service_order_update`             | `ome`                 | 在订单编辑完成后，触发一个 Miele 特定的订单更新。                                 |

## 2. UI 数据查找器 (Finder)

该模块还提供了一组 "finder" 服务。这些不是传统意义上的 API，而是由桌面/管理 UI 用于为 Miele 特定的对象填充数据表和搜索表单。

- `desktop_finder.miele_mdl_sales_material_setmapping`
- `desktop_finder.miele_mdl_reship_wo`
- `desktop_finder.miele_mdl_shop_customer_mapping`
- `desktop_finder.miele_mdl_sap_so`
- `desktop_finder.miele_mdl_sap_so_bucha`
- `desktop_finder.miele_mdl_sap_goods`
- `desktop_finder.miele_mdl_stock_masterdata`
- `desktop_finder.miele_mdl_stock`
- `desktop_finder.miele_mdl_sap_odn`

## 3. 外部 API (需要进一步调研)

`openapi.conf` 的服务注册表明该模块可能暴露或使用某种形式的 OpenAPI 文档端点。需要对 `miele_openapi_conf` 类和控制器类进行进一步调研，以识别和记录任何外部 HTTP API。