# 内部框架: 视图与模板速查表

## 1. 场景与目标

- **解决了什么问题**: 本速查表解释了如何从控制器渲染视图模板，以及如何将数据从控制器传递到视图。
- **最终产出**: 一个控制器操作，使用模板文件和应用程序数据渲染动态 HTML 页面。

---

## 2. 先决条件

- 已按照“路由与控制器速查表”中的说明设置了控制器和操作。
- 具备基本的 HTML 知识。模板引擎似乎非常接近原生 PHP 或一种简单的自定义语法。

---

## 3. 标准执行步骤

### **步骤 1: 从控制器向视图传递数据**

控制器使用一个 `pagedata` 属性（它是一个数组）来存储所有应在视图中可用的数据。

- **操作**: 在您的控制器操作中，将变量分配给 `$this->pagedata` 数组。数组的键将成为模板中的变量名。

- **代码示例**: 将用户数据和产品列表传递给视图。

  ```php
  // 在控制器方法中
  public function showProfile()
  {
      // 从模型中获取一些数据
      $userModel = app::get('desktop')->model('users');
      $userInfo = $userModel->dump(['user_id' => 1]);

      $productModel = app::get('b2c')->model('products');
      $products = $productModel->getList('product_id, name', ['marketable' => 'true']);

      // 将数据分配给 pagedata
      $this->pagedata['user'] = $userInfo;
      $this->pagedata['productList'] = $products;
      $this->pagedata['pageTitle'] = '用户资料';

      // page() 方法将渲染视图
      $this->page('admin/profile/show.html');
  }
  ```

### **步骤 2: 创建和使用视图模板**

视图文件是简单的 HTML 文件（或 PHP 文件，引擎似乎很灵活），可以包含特殊的模板标签来显示数据和执行基本逻辑。

- **操作**: 在 `app/miele/view/` 目录中创建一个 `.html` 文件。使用模板语法访问从控制器传递的数据。

- **模板语法 (基于对类似框架的分析):**
    - **显示变量**: `{$variableName}`
    - **循环**: `<{foreach from=$productList item=product}> ... <{/foreach}>`
    - **条件判断**: `<{if $user.is_admin}> ... <{/if}>`
    - **包含其他模板**: `<{include file="path/to/header.html"}>`

- **代码示例**:

  ```html
  <!-- app/miele/view/admin/profile/show.html -->

  <h2>{$pageTitle}</h2>

  <p>欢迎, {$user.name}!</p>

  <h3>您的产品:</h3>
  <ul>
    <{foreach from=$productList item=product}>
      <li>{$product.name} (ID: {$product.product_id})</li>
    <{/foreach}>
  </ul>

  <{if !$productList}>
    <p>您没有任何产品。</p>
  <{/if}>
  ```

### **步骤 3: 从控制器渲染视图**

`desktop_controller` 的 `page()` 方法用于渲染最终输出。

- **操作**: 在控制器操作的末尾调用 `$this->page('path/to/template.html')`。路径是相对于 `app/[app_name]/view/` 目录的。

- **代码示例**:

  ```php
  class miele_ctl_admin_profile extends desktop_controller
  {
      public function show()
      {
          // ... 设置 pagedata ...
          $this->pagedata['user'] = [...];

          // 渲染视图
          $this->page('admin/profile/show.html');
      }
  }
  ```

---

## 4. 最佳实践与注意事项

- **`pagedata` 是关键**: 所有数据都必须通过 `$this->pagedata` 数组传递给视图。
- **保持视图逻辑简单**: 视图应包含最少的逻辑。复杂的决策和数据处理应在控制器或模型层处理。
- **模板路径**: 提供给 `page()` 方法的路径至关重要，必须正确指向 `view` 目录内的模板文件。
- **模板引擎**: 确切的模板引擎可能是 Shopex 的自定义引擎。它类似于 Smarty，但可能有自己的特定标签和修饰符。需要进一步研究 `desktop_controller` 的 `page` 方法以确认完整的语法。

---

## 5. 来源与参考

- **源分析**: 对 `app/miele/controller/admin/sales/material/setmapping.php` 及其对 `$this->pagedata` 和 `$this->page()` 的使用的分析。