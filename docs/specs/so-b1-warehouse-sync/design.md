# 技术文档 - 销售订单(SO)处理B1类商品的仓库逻辑优化

---

## Document Information

- **Feature Name**: 销售订单(SO)处理B1类商品的仓库逻辑优化
- **Version**: 1.0
- **Date**: 2025-01-28
- **Author**: AI Assistant
- **Reviewers**: 技术架构师、订单处理团队负责人
- **Related Documents**: [需求文档](requirements.md)

## Overview

本设计文档描述了如何在现有的 miele 订单处理系统中实现B1类商品的仓库自动分配和ESB同步优化。设计采用最小侵入性原则，在不破坏现有架构的前提下，通过扩展现有组件来实现新功能。

### Design Goals
- 实现B1类商品的准确识别和仓库自动分配
- 优化ESB同步逻辑，确保大家电B1品包含仓库信息
- 保持系统性能和稳定性
- 遵循现有的代码规范和架构模式

### Key Design Decisions
- 在 miele_func 中新增 isB1Product() 方法作为统一的B1商品判断入口
- 复用现有的 ome_ctl_admin_order::getDefaultBranch() 方法进行仓库分配
- 在 ESB 同步逻辑中增加条件判断，而非修改核心同步流程
- 使用现有的数据库表结构，通过 sap_so_items.branch_id 字段存储仓库信息

## Architecture

### System Context
本功能涉及订单处理、仓库管理和ESB集成三个主要系统模块。通过在订单处理流程中集成B1商品识别和仓库分配逻辑，确保数据在ESB同步时的完整性。

```mermaid
graph TB
    A[订单创建/更新] --> B[B1商品判断]
    B --> C{是否B1商品}
    C -->|是| D[获取默认仓库]
    C -->|否| E[跳过仓库分配]
    D --> F[存储仓库信息]
    F --> G[ESB同步]
    E --> G
    G --> H{大家电B1品}
    H -->|是| I[包含仓库信息]
    H -->|否| J[不包含仓库信息]
```

### High-Level Architecture
系统采用分层架构，通过公共函数库提供B1商品判断能力，在业务逻辑层实现仓库分配，在集成层处理ESB同步逻辑。

```mermaid
graph LR
    A[公共函数层] --> B[业务逻辑层]
    B --> C[数据访问层]
    B --> D[集成服务层]
    D --> E[ESB系统]
```

### Technology Stack
| Layer | Technology | Rationale |
|-------|------------|-----------|
| 业务逻辑 | PHP Classes | 遵循现有架构模式 |
| 数据访问 | dbeav_model | 使用现有ORM框架 |
| 数据库 | MySQL | 现有数据库系统 |
| 集成 | ESB Service | 现有集成架构 |

## Components and Interfaces

### Component 1: B1商品判断组件 (miele_func::isB1Product)

**Purpose**: 提供统一的B1类商品判断逻辑

**Responsibilities**:
- 根据物料ID查询销售物料信息
- 获取商品分类信息
- 判断是否为B1类商品

**Interfaces**:
- **Input**: $materialId (物料ID)
- **Output**: boolean (true表示B1商品，false表示非B1商品)
- **Dependencies**: sales_material 表、customer_classify 表

**Implementation Notes**:
- 使用 db_dump 方法进行数据库查询以提高性能
- 实现错误处理，查询失败时返回 false
- 添加日志记录便于问题排查

### Component 2: 订单处理增强组件

**Purpose**: 在订单创建和更新时集成B1商品仓库分配逻辑

**Responsibilities**:
- 遍历订单商品行项目
- 调用B1商品判断逻辑
- 获取并存储默认仓库信息

**Interfaces**:
- **Input**: 订单信息和商品列表
- **Output**: 更新后的订单数据
- **Dependencies**: miele_func::isB1Product(), ome_ctl_admin_order::getDefaultBranch()

**Implementation Notes**:
- 在现有的 create_order 和 update_order 方法中添加处理逻辑
- 使用事务确保数据一致性
- 仓库分配失败时记录错误但不中断订单处理

### Component 3: ESB同步增强组件

**Purpose**: 在ESB同步时根据商品特性决定是否包含仓库信息

**Responsibilities**:
- 判断商品是否为大家电B1品
- 格式化仓库信息
- 构建包含仓库信息的同步报文

**Interfaces**:
- **Input**: SO单数据和商品信息
- **Output**: 格式化的ESB同步报文
- **Dependencies**: miele_func::isB1Product(), miele_func::getBranchParams()

**Implementation Notes**:
- 在现有的 _getSoItems 方法中添加仓库信息处理逻辑
- 保持向下兼容，不影响非B1商品的同步
- 使用现有的 getBranchParams 方法确保数据格式一致性

## Data Models

### Entity 1: B1商品判断数据流

```php
interface B1ProductCheck {
    materialId: string;          // 物料ID
    salesMaterialInfo: {         // 销售物料信息
        sm_id: number;
        class_id: number;
        sales_material_bn: string;
    };
    classifyInfo: {              // 分类信息
        class_id: number;
        class_name: string;
        class_bn: string;
    };
    isB1Product: boolean;        // 判断结果
}
```

### Entity 2: 仓库分配数据模型

```php
interface WarehouseAssignment {
    order_item_id: number;       // 订单行项目ID
    material_id: string;         // 物料ID
    branch_id: number;           // 仓库ID
    branch_bn: string;           // 仓库编码
    assignment_time: timestamp;   // 分配时间
}
```

### Data Flow

```mermaid
sequenceDiagram
    participant Order as 订单处理
    participant Func as miele_func
    participant DB as 数据库
    participant ESB as ESB同步
    
    Order->>Func: isB1Product(materialId)
    Func->>DB: 查询sales_material
    DB-->>Func: 返回class_id
    Func->>DB: 查询customer_classify
    DB-->>Func: 返回class_name
    Func-->>Order: 返回判断结果
    Order->>Order: getDefaultBranch()
    Order->>DB: 更新sap_so_items
    Order->>ESB: 触发同步
    ESB->>Func: 判断大家电B1品
    ESB->>Func: getBranchParams()
    ESB-->>ESB: 构建同步报文
```

## API Design

### Method 1: isB1Product

**Method**: `public static function isB1Product($materialId)`  
**Class**: `miele_func`

**Parameters**:
- `$materialId` (string): 物料ID

**Response**:
```php
// 成功返回
return true;  // 或 false

// 异常情况
return false; // 查询失败或数据不存在
```

**Error Handling**:
- 参数为空时返回 false
- 数据库查询异常时返回 false 并记录日志
- 数据不存在时返回 false

### Method 2: Enhanced create_order

**Method**: `public function create_order($orderInfo)`  
**Class**: `miele_order`

**Enhanced Logic**:
```php
// 在处理订单商品时添加
foreach ($orderItems as $item) {
    if (miele_func::isB1Product($item['material_id'])) {
        $branchId = $this->getDefaultBranch($orderInfo);
        // 更新 sap_so_items 表
    }
}
```

## Security Considerations

### Authentication
- 使用现有的应用认证机制
- 所有数据库操作通过现有的模型层进行

### Authorization
- 遵循现有的权限控制模式
- 仓库分配操作仅限于订单处理流程

### Data Protection
- 敏感数据访问遵循现有的数据保护策略
- 日志记录时避免记录敏感信息

### Input Validation
- 对输入的物料ID进行格式验证
- 数据库查询使用参数化查询防止SQL注入

## Error Handling

### Error Categories
| Category | Description | User Action |
|----------|-------------|-------------|
| 数据验证错误 | 物料ID格式不正确 | 检查输入数据格式 |
| 数据库连接错误 | 数据库连接失败 | 检查数据库连接状态 |
| 数据不存在 | 物料或分类信息不存在 | 确认数据完整性 |
| 仓库分配失败 | 无法获取默认仓库 | 检查仓库配置 |

### Error Response Format
```php
// 日志记录格式
$logData = [
    'action' => 'B1Product_Check',
    'material_id' => $materialId,
    'error' => $errorMessage,
    'timestamp' => time(),
    'trace' => debug_backtrace()
];
```

### Logging Strategy
- **错误日志**: 记录所有异常和错误情况
- **业务日志**: 记录B1商品判断和仓库分配结果
- **性能日志**: 记录关键操作的执行时间

## Performance Considerations

### Expected Load
- **并发订单处理**: 50个/秒
- **B1商品判断请求**: 200次/秒
- **数据库查询**: 平均2次/判断

### Performance Requirements
- **B1商品判断响应时间**: < 100ms
- **仓库分配响应时间**: < 200ms
- **ESB同步响应时间**: 保持现有水平

### Optimization Strategies
- 使用 db_dump 而非 dump 方法提高查询性能
- 考虑添加 class_id 的缓存机制
- 批量处理时使用 IN 查询减少数据库访问次数

### Monitoring and Metrics
- 监控B1商品判断的成功率和响应时间
- 监控仓库分配的成功率
- 监控ESB同步的性能影响

## Testing Strategy

### Unit Testing
- **Coverage Target**: 90%
- **Testing Framework**: PHPUnit
- **Key Test Areas**: B1商品判断逻辑、仓库分配逻辑、错误处理

### Integration Testing
- **数据库测试**: 验证查询逻辑的正确性
- **组件集成测试**: 验证订单处理和ESB同步的集成
- **性能测试**: 验证新功能对系统性能的影响

### End-to-End Testing
- **订单处理场景**: 创建包含B1商品的订单并验证仓库分配
- **ESB同步场景**: 验证大家电B1品的仓库信息传递
- **异常场景**: 验证各种异常情况的处理

## Deployment and Operations

### Deployment Strategy
- 采用灰度发布策略，先在测试环境验证
- 生产环境部署时先部署判断逻辑，再启用仓库分配
- 提供回滚方案，可快速禁用新功能

### Configuration Management
- 通过配置文件控制功能开关
- 支持动态调整B1商品判断的缓存策略

### Monitoring and Alerting
- 监控B1商品判断的错误率
- 监控仓库分配失败的情况
- 设置ESB同步异常的告警

---

## 参考资料

- **参考的 Cheatsheet**: 
  - `docs/cheatsheet/system/class-loading-rules.md`
  - `docs/cheatsheet/database/query-basic.md`
- **参考的 Reference 文档**: 无
- **参考的 URL**: 无

## 产出评估

- **是否需要生成新的 Cheatsheet?**: 否
  - **理由**: 本次开发主要是业务逻辑的扩展，没有引入新的通用技术模式
  
- **是否需要生成 Reference 文档?**: 否
  - **理由**: 功能相对简单，现有的技术文档已足够详细
