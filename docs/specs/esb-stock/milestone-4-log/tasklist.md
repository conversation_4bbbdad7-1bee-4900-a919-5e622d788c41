---
# 里程碑4: 操作日志配置调整
# 状态: 待处理
# 源任务: docs/specs/SAP库存同步重构/tasks.md 中的里程碑4
---

> **核心原则**: 本文档是 AI 的精确执行手册。每个任务都应明确、原子化，并包含清晰的 `执行` 和 `验证` 步骤，确保操作的准确性和可追溯性。

---

- [ ] **任务 4.1: 验证当前操作日志配置** `[待处理]`
  - **目标文件**: `app/miele/lib/operation/log.php`
  - **操作**: `检查`
  - **执行**:
    - **查看当前操作日志配置**:
      ```shell
      grep -A 20 "get_operations" app/miele/lib/operation/log.php
      ```
    - **查看库存相关的操作类型**:
      ```shell
      grep -B 2 -A 2 "stock.*=>" app/miele/lib/operation/log.php
      ```
  - **验证**:
    ```shell
    php -l app/miele/lib/operation/log.php && echo "语法检查通过"
    ```

---

- [ ] **任务 4.2: 验证手动库存同步的操作日志记录** `[待处理]`
  - **目标文件**: `app/miele/controller/admin/stock/masterdata.php`
  - **操作**: `检查`
  - **执行**:
    - **查看sync_stock方法中的操作日志**:
      ```shell
      grep -A 10 -B 5 "write_log.*stock" app/miele/controller/admin/stock/masterdata.php
      ```
    - **确认使用正确的主键ID**:
      ```shell
      grep -B 5 -A 5 "material_info\['id'\]" app/miele/controller/admin/stock/masterdata.php
      ```
  - **验证**:
    ```shell
    php -l app/miele/controller/admin/stock/masterdata.php && echo "语法检查通过"
    ```

---

- [ ] **任务 4.3: 分析操作日志精简需求** `[待处理]`
  - **目标文件**: `app/miele/lib/operation/log.php`
  - **操作**: `分析`
  - **执行**:
    - **确认需要保留的操作类型**:
      ```shell
      echo "只保留：stock_masterdata_sync - 手动同步库存"
      ```
    - **确认需要移除的操作类型**:
      ```shell
      echo "移除：库存调整单生成相关的操作日志（状态已在数据库表中记录）"
      ```
  - **验证**:
    ```shell
    echo "操作日志精简需求分析完成"
    ```

---
