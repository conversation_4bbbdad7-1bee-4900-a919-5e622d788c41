# Task 2: 增强create_order方法集成B1商品仓库分配

## 任务概述
在 `app/miele/lib/order.php` 的 `create_order` 方法中集成B1商品仓库自动分配逻辑，确保B1商品在订单创建时自动获得默认仓库分配。

## 详细设计

### 文件位置
- **目标文件**: `app/miele/lib/order.php`
- **修改方法**: `create_order($orderInfo)`
- **新增私有方法**: `assignWarehouseForB1Products($orderInfo)`, `getDefaultBranchForB1Product($orderId)`

### 核心实现逻辑

#### 1. 修改create_order主方法

```php
public function create_order($orderInfo) {
    // 原有订单创建逻辑保持不变...
    // [原有代码不做修改]
    
    // 新增：B1商品仓库分配逻辑
    try {
        kernel::log('info', 'create_order: starting B1 warehouse assignment', [
            'order_id' => $orderInfo['order_id'] ?? 'unknown',
            'so_id' => $orderInfo['so_id'] ?? 'unknown'
        ]);
        
        $this->assignWarehouseForB1Products($orderInfo);
        
        kernel::log('info', 'create_order: B1 warehouse assignment completed', [
            'order_id' => $orderInfo['order_id'] ?? 'unknown'
        ]);
        
    } catch (Exception $e) {
        // 仓库分配失败不中断订单创建流程
        kernel::log('error', 'create_order: B1 warehouse assignment failed', [
            'order_id' => $orderInfo['order_id'] ?? 'unknown',
            'error_message' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        // 继续执行，不抛出异常
    }
    
    // 继续原有的订单创建逻辑...
    // [原有代码不做修改]
}
```

#### 2. 实现B1商品仓库分配核心方法

```php
/**
 * 为B1商品分配默认仓库
 * 
 * 遍历订单中的所有商品，识别B1类商品并为其分配默认仓库
 * 仓库信息存储在sap_so_items表的branch_id字段中
 * 
 * @param array $orderInfo 订单信息，必须包含order_id和so_id
 * @throws Exception 当关键参数缺失或数据库操作失败时
 */
private function assignWarehouseForB1Products($orderInfo) {
    // 参数验证
    if (empty($orderInfo['order_id']) || empty($orderInfo['so_id'])) {
        kernel::log('warning', 'assignWarehouseForB1Products: missing required parameters', [
            'order_info' => $orderInfo
        ]);
        return;
    }
    
    $orderId = $orderInfo['order_id'];
    $soId = $orderInfo['so_id'];
    $processedCount = 0;
    $assignedCount = 0;
    
    try {
        // 获取订单商品信息
        $soItemsMdl = app::get('miele')->model('sap_so_items');
        $soItems = $soItemsMdl->getList('*', [
            'so_id' => $soId
        ]);
        
        if (empty($soItems)) {
            kernel::log('info', 'assignWarehouseForB1Products: no items found', [
                'order_id' => $orderId,
                'so_id' => $soId
            ]);
            return;
        }
        
        kernel::log('info', 'assignWarehouseForB1Products: processing items', [
            'order_id' => $orderId,
            'so_id' => $soId,
            'item_count' => count($soItems)
        ]);
        
        // 遍历处理每个商品
        foreach ($soItems as $item) {
            $processedCount++;
            
            try {
                // 验证商品数据完整性
                if (empty($item['sales_material_bn'])) {
                    kernel::log('warning', 'assignWarehouseForB1Products: missing material_bn', [
                        'order_id' => $orderId,
                        'item_id' => $item['id'] ?? 'unknown'
                    ]);
                    continue;
                }
                
                // 判断是否为B1商品
                $isB1Product = miele_func::isB1Product($item['sales_material_bn']);
                
                if ($isB1Product) {
                    // 检查是否已分配仓库
                    if (!empty($item['branch_id'])) {
                        kernel::log('info', 'assignWarehouseForB1Products: warehouse already assigned', [
                            'order_id' => $orderId,
                            'item_id' => $item['id'],
                            'material_bn' => $item['sales_material_bn'],
                            'existing_branch_id' => $item['branch_id']
                        ]);
                        continue;
                    }
                    
                    // 获取默认仓库
                    $branchId = $this->getDefaultBranchForB1Product($orderId);
                    
                    if ($branchId) {
                        // 更新仓库信息
                        $updateData = [
                            'branch_id' => $branchId,
                            'updated_time' => time()
                        ];
                        
                        $updateResult = $soItemsMdl->update($updateData, [
                            'id' => $item['id']
                        ]);
                        
                        if ($updateResult) {
                            $assignedCount++;
                            kernel::log('info', 'assignWarehouseForB1Products: warehouse assigned successfully', [
                                'order_id' => $orderId,
                                'item_id' => $item['id'],
                                'material_bn' => $item['sales_material_bn'],
                                'branch_id' => $branchId
                            ]);
                        } else {
                            kernel::log('error', 'assignWarehouseForB1Products: warehouse assignment failed', [
                                'order_id' => $orderId,
                                'item_id' => $item['id'],
                                'material_bn' => $item['sales_material_bn'],
                                'branch_id' => $branchId
                            ]);
                        }
                    } else {
                        kernel::log('warning', 'assignWarehouseForB1Products: no default warehouse available', [
                            'order_id' => $orderId,
                            'item_id' => $item['id'],
                            'material_bn' => $item['sales_material_bn']
                        ]);
                    }
                }
                
            } catch (Exception $e) {
                // 单个商品处理失败不影响其他商品
                kernel::log('error', 'assignWarehouseForB1Products: item processing failed', [
                    'order_id' => $orderId,
                    'item_id' => $item['id'] ?? 'unknown',
                    'material_bn' => $item['sales_material_bn'] ?? 'unknown',
                    'error_message' => $e->getMessage()
                ]);
                continue;
            }
        }
        
        // 记录处理结果统计
        kernel::log('info', 'assignWarehouseForB1Products: processing completed', [
            'order_id' => $orderId,
            'processed_count' => $processedCount,
            'assigned_count' => $assignedCount,
            'success_rate' => $processedCount > 0 ? round(($assignedCount / $processedCount) * 100, 2) . '%' : '0%'
        ]);
        
    } catch (Exception $e) {
        kernel::log('error', 'assignWarehouseForB1Products: critical error', [
            'order_id' => $orderId,
            'so_id' => $soId,
            'processed_count' => $processedCount,
            'assigned_count' => $assignedCount,
            'error_message' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
        throw $e; // 重新抛出异常供上层处理
    }
}
```

#### 3. 实现默认仓库获取方法

```php
/**
 * 获取B1商品的默认仓库ID
 * 
 * 调用现有的ome_ctl_admin_order::getDefaultBranch方法获取默认仓库
 * 该方法复用现有的仓库分配逻辑，确保一致性
 * 
 * @param string $orderId 订单ID
 * @return int|null 仓库ID，获取失败时返回null
 */
private function getDefaultBranchForB1Product($orderId) {
    try {
        $startTime = microtime(true);
        
        // 调用现有的默认仓库获取逻辑
        $orderCtl = new ome_ctl_admin_order();
        $branchResult = $orderCtl->getDefaultBranch([$orderId]);
        
        $executionTime = round((microtime(true) - $startTime) * 1000, 2);
        
        // 处理返回结果
        if (is_array($branchResult) && !empty($branchResult)) {
            // 获取第一个有效的仓库ID
            $branchId = null;
            foreach ($branchResult as $result) {
                if (!empty($result) && is_numeric($result)) {
                    $branchId = (int)$result;
                    break;
                }
            }
            
            if ($branchId) {
                kernel::log('info', 'getDefaultBranchForB1Product: success', [
                    'order_id' => $orderId,
                    'branch_id' => $branchId,
                    'execution_time' => $executionTime . 'ms'
                ]);
                return $branchId;
            }
        }
        
        kernel::log('warning', 'getDefaultBranchForB1Product: no valid branch found', [
            'order_id' => $orderId,
            'branch_result' => $branchResult,
            'execution_time' => $executionTime . 'ms'
        ]);
        return null;
        
    } catch (Exception $e) {
        kernel::log('error', 'getDefaultBranchForB1Product: exception occurred', [
            'order_id' => $orderId,
            'error_message' => $e->getMessage(),
            'error_code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        return null;
    }
}
```

### 集成策略

#### 1. 最小侵入原则
- 在原有create_order方法末尾添加B1处理逻辑
- 不修改原有的订单创建流程
- 异常处理确保不影响订单创建成功

#### 2. 错误处理策略
- 仓库分配失败不中断订单创建
- 详细记录每个步骤的执行情况
- 提供完整的错误追踪信息

#### 3. 性能考虑
- 批量获取订单商品，减少数据库查询次数
- 记录执行时间，便于性能监控
- 异常情况快速失败，避免资源浪费

### 数据流程图

```mermaid
sequenceDiagram
    participant CO as create_order
    participant AWB as assignWarehouseForB1Products
    participant MF as miele_func
    participant GDB as getDefaultBranchForB1Product
    participant DB as Database
    
    CO->>AWB: 调用仓库分配
    AWB->>DB: 获取订单商品列表
    DB-->>AWB: 返回商品数据
    
    loop 遍历每个商品
        AWB->>MF: isB1Product(materialId)
        MF-->>AWB: 返回判断结果
        
        alt 是B1商品且未分配仓库
            AWB->>GDB: 获取默认仓库
            GDB-->>AWB: 返回仓库ID
            AWB->>DB: 更新仓库信息
            DB-->>AWB: 确认更新成功
        end
    end
    
    AWB-->>CO: 返回处理结果
```

### 测试用例设计

#### 正常流程测试
1. **B1商品仓库分配**: 验证B1商品正确分配仓库
2. **非B1商品跳过**: 验证非B1商品不受影响
3. **已分配仓库跳过**: 验证已有仓库的商品不重复分配

#### 异常情况测试
1. **订单信息缺失**: 验证参数验证逻辑
2. **商品信息不完整**: 验证数据完整性检查
3. **仓库获取失败**: 验证错误处理机制
4. **数据库更新失败**: 验证事务处理

#### 性能测试
1. **大订单处理**: 验证包含多个商品的订单处理性能
2. **并发订单**: 验证多个订单同时创建的性能
3. **响应时间**: 验证仓库分配不显著影响订单创建时间

### 验收标准

#### 功能验收
- [ ] B1商品在订单创建时自动分配默认仓库
- [ ] 非B1商品不受影响
- [ ] 已分配仓库的B1商品不重复分配
- [ ] 仓库分配失败不影响订单创建成功

#### 性能验收
- [ ] 仓库分配逻辑执行时间<200ms
- [ ] 订单创建总时间增加<10%
- [ ] 并发处理无性能瓶颈

#### 质量验收
- [ ] 错误处理完善，异常不影响主流程
- [ ] 日志记录详细，便于问题排查
- [ ] 代码符合项目规范
- [ ] 单元测试覆盖率>90%

---

## 实施步骤

### Step 1: 代码实现
1. 修改 `create_order` 方法添加B1处理调用
2. 实现 `assignWarehouseForB1Products` 方法
3. 实现 `getDefaultBranchForB1Product` 方法

### Step 2: 单元测试
1. 创建测试用例覆盖所有场景
2. 模拟各种异常情况
3. 验证性能要求

### Step 3: 集成测试
1. 在开发环境测试完整流程
2. 验证与现有订单创建流程的兼容性
3. 测试各种订单类型和商品组合

### Step 4: 性能测试
1. 测试大订单处理性能
2. 测试并发订单创建
3. 监控数据库查询性能

---

## 相关文档
- **需求文档**: [requirements.md](requirements.md) - Requirement 2
- **设计文档**: [design.md](design.md) - Component 2
- **Task 1**: [task-1.md](task-1.md) - 依赖的B1商品判断逻辑
