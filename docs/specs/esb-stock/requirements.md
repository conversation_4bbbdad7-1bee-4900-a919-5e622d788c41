# SAP库存同步重构需求分析

## 1. 项目背景

当前SAP库存同步系统需要重构，以提高同步效率和数据准确性。现有的库存同步方法需要优化，支持批次管理和更好的错误处理机制。

## 2. 核心需求

### 2.1 功能需求

#### 2.1.1 库存同步入口重构
- **入口方法**: `syncStock`
- **输入参数**: 基础物料编码数组（支持多个物料编码）
- **返回值**: 同步结果状态和详细信息

#### 2.1.2 批次号管理
- **需求**: 每次请求ESB接口都需要定义唯一批次号
- **约束**: 批次号不能与历史批次号重复
- **参考**: 参考发货单号生成机制
- **格式**: 基于时间戳和递增序号的组合

#### 2.1.3 发货仓库列表获取
- **筛选条件**: 
  - 仓库类型为发货仓库（`is_deliv_branch = 'true'`）
  - 仓库类型为主仓（`type = 'main'`）
  - 仓库状态为启用（`disabled = 'false'`）
- **编码格式验证**: 仓库编码必须符合"工厂编码:货位编码"格式
- **处理策略**: 不符合格式的仓库跳过同步

#### 2.1.4 ESB接口调用
- **循环处理**: 遍历每个有效发货仓库
- **请求参数**: 仓库编码（解析为工厂编码和货位编码）+ 基础物料编码数组
- **日志处理**: ESB接口已有接口日志，无需额外操作日志

#### 2.1.5 历史数据状态管理
- **时机**: 在获取ESB库存前执行
- **操作**: 将`sdb_miele_stock`表中未同步的数据状态改为"不需要同步"（`sync_status = 'none'`）
- **保留**: 已同步成功的数据状态保持不变

#### 2.1.6 库存数据记录
- **目标表**: `sdb_miele_stock`
- **数据来源**: ESB接口返回的库存信息
- **用途**: 后续将库存同步到OMS库存表

#### 2.1.7 库存调整单生成
- **数据源**: `sdb_miele_stock`表中未同步到OMS的库存记录
- **方式**: 通过库存调整单更新到OMS库存表
- **状态更新**: 成功生成调整单后更新同步状态

### 2.2 调用场景

#### 2.2.1 定时任务调用
- **入口文件**: `app/miele/lib/autotask/timer/stocksync.php`
- **方法**: `process`方法需要同步修改
- **数据获取**: 批量获取`stock_masterdata`表中的基础物料编码
- **分组策略**: 将物料编码分组后调用`syncStock`方法

#### 2.2.2 控制器手动调用
- **控制器**: `miele_ctl_admin_stock_masterdata`
- **方法**: `sync_stock`方法需要重构
- **输入参数**: `material_ids`参数数组
- **处理逻辑**: 通过`material_ids`获取基础物料编码，分组调用`syncStock`

## 3. 技术约束

### 3.1 数据库约束
- **批次号唯一性**: 必须确保批次号在系统中唯一
- **事务处理**: 关键操作需要事务保护
- **索引优化**: 确保查询性能

### 3.2 接口约束
- **ESB接口限制**: 遵循现有ESB接口规范
- **超时处理**: 合理设置接口超时时间
- **重试机制**: 对失败的接口调用实现重试

### 3.3 性能约束
- **批量处理**: 支持大量物料的批量同步
- **内存控制**: 避免大数据量导致的内存溢出
- **并发控制**: 防止重复同步导致的数据冲突

## 4. 数据流程

### 4.1 同步流程
1. 生成唯一批次号
2. 验证批次号唯一性
3. 获取有效发货仓库列表
4. 验证仓库编码格式
5. 更新历史数据状态
6. 循环调用ESB接口获取库存
7. 记录库存数据到`sdb_miele_stock`表
8. 生成库存调整单
9. 更新同步状态

### 4.2 数据状态管理
- **pending**: 待处理
- **processing**: 处理中  
- **success**: 成功
- **failed**: 失败
- **none**: 无需同步

## 5. 错误处理

### 5.1 异常场景
- 批次号重复
- 仓库编码格式错误
- ESB接口调用失败
- 数据库操作失败
- 库存调整单生成失败

### 5.2 处理策略
- 详细的错误日志记录
- 失败数据的状态标记
- 部分失败时的继续处理机制
- 用户友好的错误信息返回

## 6. 验收标准

### 6.1 功能验收
- [ ] `syncStock`方法能正确处理多个物料编码
- [ ] 批次号生成机制正常工作且保证唯一性
- [ ] 仓库编码格式验证正确
- [ ] ESB接口调用成功并正确处理返回数据
- [ ] 历史数据状态更新正确
- [ ] 库存数据正确记录到`sdb_miele_stock`表
- [ ] 库存调整单生成正常
- [ ] 定时任务调用正常
- [ ] 控制器手动调用正常

### 7.1 技术风险
- **数据一致性风险**: 并发同步可能导致数据不一致
- **性能风险**: 大量数据同步可能影响系统性能
- **接口依赖风险**: ESB接口不稳定可能影响同步

### 7.2 业务风险
- **库存准确性风险**: 同步失败可能导致库存数据不准确
- **业务中断风险**: 重构过程中可能影响现有业务

### 7.3 风险缓解措施
- 充分的测试验证

## 8. 依赖关系

### 8.1 系统依赖
- ESB接口服务
- 数据库服务
- 定时任务调度系统

### 8.2 数据依赖
- `stock_masterdata`表（物料白名单）
- `sdb_miele_stock`表（库存同步记录）
- `ome_branch`表（仓库信息）
- `material_basic_material`表（基础物料信息）

### 8.3 代码依赖
- `miele_esb_stock`类（ESB接口调用）
- `miele_stock_adjust`类（库存调整单生成）
- 现有的批次号生成机制
