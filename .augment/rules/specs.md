---
type: "agent_requested"
description: "kiro specs 工作流"
---
# Spec 工作流核心规范

## 1. 核心理念与原则

Spec 工作流旨在将模糊的想法转化为清晰、可执行的实施计划，并通过分层任务管理和自动化执行，确保开发过程的精确性、可追溯性和健壮性。

### **核心原则**

- **代码之前先明确 (Clarity Before Code)**: 思维和目的的清晰必须先于实施，投入时间理解需求、设计解决方案和规划实施
- **迭代完善 (Iterative Refinement)**: 每个阶段都设计为迭代的，在每个步骤进行完善和验证，在问题修复成本较低时及早发现问题
- **文档作为沟通 (Documentation as Communication)**: Spec 不仅仅是规划文档，更是沟通工具，协调利益相关者，保存决策理由
- **质量关卡验证 (Quality Gates)**: 每一步操作都必须有明确的验证标准和完成定义，确保结果符合预期
- **约定大于配置**：流程和决策应直观、可复用。
- **可重复执行**: 详细的执行脚本必须包含所有必要信息，确保 AI 多次执行结果一致。
- **文档同步**: 任何代码或设计的变更都必须同步更新到相关文档。
- **需求可追溯性 (Requirements Traceability)**: 从需求到设计到任务的清晰链接，确保每个实施都能追溯到原始需求

---

## **设计和执行阶段必须符合以下规范**
- 当前项目类的加载机制和文件创建规范: `docs/cheatsheet/system/class-loading-rules.md`
- 当前项目数据库查询手册: `docs/cheatsheet/database/query-basic.md`
- 当前数据库表结构配置手册: `docs/cheatsheet/database/schema-config.md`
- 当前项目前端页面开发手册: `docs/cheatsheet/frontend/form-design.md`

## 2. 三阶段工作流程与目录结构

在需求分析，技术设计，高级任务规划，详细任务规划过程中禁止直接修改代码，只有在实际执行详细任务才允许按照详细任务的文档进行开发。

### **阶段概览**

```mermaid
stateDiagram-v2
    [*] --> 需求收集 : 功能想法
    需求收集 --> 需求审查 : 初稿完成
    需求审查 --> 需求收集 : 需要修改
    需求审查 --> 设计文档 : 需求批准
    
    设计文档 --> 设计审查 : 设计完成
    设计审查 --> 设计文档 : 需要修改
    设计审查 --> 任务分解 : 设计批准
    
    任务分解 --> 任务审查 : 任务完成
    任务审查 --> 任务分解 : 需要调整
    任务审查 --> 实施准备 : 任务批准
    
    实施准备 --> [*] : 开始编码
```

### **规格级别选择**

根据项目复杂度和风险级别，自动选择合适的规格级别：

#### 微规格 (Micro Spec) - 适用于 < 1天工作量
- **适用场景**: 错误修复、配置更新、简单变更
- **文档要求**: 最小化文档（问题描述 + 解决方案 + 验证）
- **质量关卡**: 基本验证和测试

#### 快速规格 (Quick Spec) - 适用于 1-3天工作量
- **适用场景**: 中等复杂度功能，技术方案相对清晰
- **文档要求**: 需求文档 + 任务分解（跳过详细设计）
- **质量关卡**: 需求验证 + 任务可执行性检查

#### 完整规格 (Full Spec) - 适用于 > 3天工作量
- **适用场景**: 复杂功能、系统集成、高风险项目
- **文档要求**: 完整的需求 → 设计 → 任务三阶段文档
- **质量关卡**: 每个阶段都有完整的质量验证


### **标准目录结构**

- **命名约定**: `[功能名称]` 应为小写英文，并使用短横线 (`-`) 分隔单词 (kebab-case)，例如：`sap-stock-sync-optimization`。
- **路径格式**: `docs/spec/[功能名称]/`

```
docs/specs/
└── [功能名称]/
    ├── requirements.md
    ├── design.md
    ├── tasks.md
    ├── tasks-tracking.md #任务跟踪
    └── context-summary.md  # (可选) 技术要点摘要，**里程碑超过2个则必须有**
```

### 3. 使用相应模板和工具


为了确保各阶段产出物的一致性，Spec 工作流为每个核心阶段提供了标准模板。在创建新 Spec 时自动应用相应模板。

| 工作流阶段 | 目标产出文件名 | 关联模板 (相对于 `docs` 目录) |
| :--- | :--- | :--- |
| 需求分析 | `requirements.md` | `templates/requirements-template.md` |
| 技术设计 | `design.md` | `templates/design-template.md` |
| 任务分解 | `tasks.md` | `templates/tasks-template.md` |
| 任务跟踪 | `tasks-tracking.md` | `templates/tasks-tracking-template.md` |
| 技术要点摘要 | `context-summary.md` | `templates/context-summary-template.md` |
| 快速规格 | `quick-spec.md` | `templates/quick-spec-template.md` |
| 微规格 | `micro-spec.md` | `templates/micro-spec-template.md` |

**规则说明**:

- **目标产出文件名**: 当 `augmentcode` 工具在 Spec 目录 (`docs/specs/[功能名称]/`) 下创建文件时，会使用此列定义的文件名。
- **关联模板**: 工具会自动读取此列指定的模板文件内容，并将其作为新创建文件的初始内容。


#### 完整规格流程
1. **需求阶段** → [templates/requirements.md](templates/requirements.md)
   - 使用 EARS 格式编写需求
   - 定义清晰的验收标准
   - 考虑非功能性需求
   - **输出**: `requirements.md`

2. **设计阶段** → templates/design.md
   - 研究技术方案和架构
   - 记录设计决策和理由
   - 规划实施策略
   - **输出**: `design.md`

3. **任务阶段** → templates/tasks.md
   - 分解为可执行的编码任务
   - 建立依赖关系和时间线
   - 定义质量检查点
   - 通过编码任务生成任务跟踪文档
   - 任务分解需要有改动文件和详细改动说明，如果任务拆分超过2个里程碑,则按照里程碑进行任务拆解,每个里程碑生成一个`tasks.md文件`
   - **输出**: [小于两个里程碑任务:`tasks.md`, `tasks-tracking.md`], [大于两个里程碑任务:`tasks-1.md`, `tasks-2.md`, `tasks-3.md`...`tasks-tracking.md`]

#### 快速规格流程
- 使用 templates/quick-spec.md
- 重点关注需求和任务
- 适用于技术方案相对明确的功能

#### 微规格流程
- 使用 templates/micro-spec.md
- 最小化文档，快速实施
- 适用于简单变更和修复

---

## 4. 大型项目上下文保持机制

对于跨越数天或包含多个复杂模块的大型项目，我们引入更精细的上下文管理机制，以确保 AI 在长时间的开发过程中保持记忆的连续性和准确性。

### **技术要点摘要 (`context-summary.md`)**

- **目标**: 从任务拆解文件中提取当前里程碑最关键的技术信息，作为 AI 执行任务时必须加载的上下文。
- **用途**: 当一个里程碑的技术细节非常复杂时，创建一个 `context-summary.md` 文件来存放核心的架构决策、接口定义、数据模型、关键约束等。这能帮助 AI 在执行任时，聚焦于最重要的技术细节，避免信息过载。
- **原则**: 每个需要它的里程碑都应独立维护自己的 `context-summary.md`，确保信息的相关性和精确性。

---

## 5. 自动化执行与验证循环

自动化执行循环**仅作用于 `tasks.md`, `tasks-x.md`, `tasks-tracking.md` 文件**。

当被指示对一个 `tasks-x.md` 文件进行操作时，AI 将遵循以下自动化循环：

1.  **加载上下文**: (如果存在) 读取当前里程碑目录下的 `context-summary.md`。
2.  **读取任务**: 读取 `tasks-tracking.md` 的内容。
3.  **定位当前任务**: 找到第一个状态为 `⏳` (进行中) 或 `⭕` (待处理) 的任务。
4.  **执行任务**: AI根据当前执行的任务跟踪记录找到`tasks-x.md`的任务描述执行具体操作。
6.  **更新任务状态**:
    - **成功 (检查通过)**: 将任务状态标记为 `✅` (已完成)。
    - **失败 (检查不通过)**: 将任务状态标记为 `⚠️` (失败)，并**在任务描述中附加失败的检查日志**，然后停止执行。
7.  **写入文件**: 将更新后的任务跟踪 `tasks-tracking.md` 文件。
8.  **自动继续**: 如果检查通过，**立即开始下一个循环**，自动处理下一个 `⭕` 状态的任务，直到全部完成或遇到错误。


### 6. 质量保证和验证

#### 使用检查清单 → templates/checklists.md
- **需求阶段检查清单**: 完整性、质量、EARS格式验证
- **设计阶段检查清单**: 需求覆盖、技术质量、可实施性
- **任务阶段检查清单**: 完整性、可执行性、质量保证

## 🔧 实际应用指南

### 场景1: 新功能开发
```
1. 使用 codebase-retrieval 了解现有代码结构
2. 根据复杂度选择规格级别
3. 使用相应模板创建规格文档
4. 应用 AI 协作策略获得帮助
5. 使用检查清单验证质量
6. 按照任务分解执行实施
```

### 场景2: 系统重构
```
1. 分析现有系统架构和问题
2. 使用完整规格流程
3. 重点关注设计阶段的技术决策
4. 使用 AI 决策框架评估方案
5. 详细的任务分解和风险管理
```

### 场景3: 错误修复
```
1. 使用微规格模板
2. 快速定义问题和解决方案
3. 最小化文档，快速实施
4. 确保测试和验证
```

## 📋 核心规则遵循

### 强制性要求
1. **信息收集**: 始终使用 `codebase-retrieval` 了解现有代码，自动检测 docs/cheatsheet中的手册适用性进行读取
2. **规格先行**: 根据复杂度选择合适的规格级别
3. **质量关卡**: 每个阶段都要通过质量检查
4. **安全约束**: 严格遵循禁止操作清单

### 最佳实践
1. **迭代完善**: 在每个阶段收集反馈并改进
2. **文档同步**: 保持文档与实施的一致性
3. **知识分享**: 记录决策理由和经验教训
4. **持续改进**: 定期回顾和优化流程