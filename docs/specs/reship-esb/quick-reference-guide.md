# 退货SAP集成开发手册 - 速查手册

## 概述

本手册提供退货SAP对接的快速开发参考，包含核心设计模式、关键代码片段和常见问题解决方案，可用于类似的SAP集成项目。

## 核心设计模式

### 1. 分层架构模式

```
┌─────────────────┐
│   用户界面层     │ ← Finder + Controller + View
├─────────────────┤
│   服务层        │ ← Service Classes (业务逻辑)
├─────────────────┤
│   ESB接口层     │ ← ESB + Callback (SAP通信)
├─────────────────┤
│   数据访问层     │ ← Model Classes (数据操作)
└─────────────────┘
```

**关键文件结构**:
- `app/miele/lib/service/reship/` - 业务服务层
- `app/miele/lib/esb/` - ESB接口层
- `app/miele/controller/admin/` - 控制器层
- `app/miele/view/admin/reship/` - 视图层

### 2. 状态机模式

**退货单状态流转**:
```
待审核 → 待质检 → 待同步 → 同步中 → 同步成功
   ↓       ↓       ↓       ↓
  拒绝   质检不通过  同步失败  补差处理
```

**实现要点**:
- 使用常量类定义所有状态
- 状态变更必须记录操作日志
- 状态验证在服务层实现

### 3. 配置驱动模式

**配置常量类设计**:
```php
class miele_service_reship_config {
    // SAP同步状态
    const SAP_SYNC_STATUS_NONE = 'none';
    const SAP_SYNC_STATUS_PENDING = 'pending';
    const SAP_SYNC_STATUS_SUCC = 'succ';
    
    // 业务状态映射
    public static function getSapSyncStatusMap() {
        return [
            self::SAP_SYNC_STATUS_NONE => '未同步',
            self::SAP_SYNC_STATUS_PENDING => '待同步',
            // ...
        ];
    }
}
```

## 快速开发模板

### 1. ESB接口模板

```php
class miele_esb_[module_name] {
    private $apiUrl;
    private $timeout = 30;
    
    public function push[Entity]($entityId) {
        try {
            // 1. 获取数据
            $data = $this->_buildRequestData($entityId);
            
            // 2. 调用SAP接口
            $response = $this->_callSapApi($data);
            
            // 3. 处理响应
            return $this->_handleResponse($response);
            
        } catch (Exception $e) {
            kernel::log('SAP接口调用失败: ' . $e->getMessage());
            return ['res' => 'fail', 'message' => $e->getMessage()];
        }
    }
    
    private function _callSapApi($data) {
        // HTTP请求实现
    }
}
```

### 2. 回调处理模板

```php
class miele_esb_callback_[module] {
    public function addCallback($params) {
        try {
            // 1. 参数验证
            if (empty($params['otherSystemSO'])) {
                return ['rsp' => 'fail', 'msg' => '参数缺失'];
            }
            
            // 2. 查找记录
            $entity = $this->_findEntity($params['otherSystemSO']);
            
            // 3. 更新状态
            $this->_updateStatus($entity, $params);
            
            // 4. 记录日志
            $this->_logOperation($entity, $params);
            
            return ['rsp' => 'succ', 'msg' => '处理成功'];
            
        } catch (Exception $e) {
            return ['rsp' => 'fail', 'msg' => $e->getMessage()];
        }
    }
}
```

### 3. 服务层模板

```php
class miele_service_[module]_[action] {
    public function process[Action]($entityId, $data) {
        try {
            // 1. 数据验证
            $validation = $this->_validateData($data);
            if (!$validation[0]) {
                return [false, ['msg' => $validation[1]]];
            }
            
            // 2. 业务逻辑处理
            $result = $this->_processBusinessLogic($entityId, $data);
            
            // 3. 状态更新
            $this->_updateEntityStatus($entityId, $result);
            
            // 4. 触发后续流程
            $this->_triggerNextProcess($entityId, $result);
            
            return [true, ['msg' => '处理成功']];
            
        } catch (Exception $e) {
            return [false, ['msg' => $e->getMessage()]];
        }
    }
}
```

### 4. 控制器模板

```php
class miele_controller_admin_[module] extends desktop_controller {
    public function [action]() {
        $this->begin('index.php?app=miele&ctl=admin_[module]&act=index');
        
        $id = $_GET['id'];
        
        if ($_POST) {
            try {
                $result = kernel::single('miele_service_[module]_[action]')
                    ->process[Action]($id, $_POST);
                    
                if (!$result[0]) {
                    $this->end(false, $result[1]['msg']);
                }
                
                $this->end(true, '操作成功');
                
            } catch (Exception $e) {
                $this->end(false, '操作失败：' . $e->getMessage());
            }
        }
        
        // 获取数据并显示页面
        $this->pagedata['data'] = $this->_getData($id);
        $this->display('admin/[module]/[action].html');
    }
}
```

## 关键技术要点

### 1. 数据库设计原则

**状态字段设计**:
```sql
-- 同步状态字段
`sap_sync_status` VARCHAR(20) DEFAULT 'none' COMMENT 'SAP同步状态'
`sap_sync_time` INT(11) DEFAULT NULL COMMENT 'SAP同步时间'
`sap_[entity]_bn` VARCHAR(50) DEFAULT NULL COMMENT 'SAP单据号'

-- 业务状态字段  
`[business]_status` VARCHAR(20) DEFAULT 'pending' COMMENT '业务状态'

-- 索引设计
KEY `idx_sap_sync_status` (`sap_sync_status`)
KEY `idx_[business]_status` (`[business]_status`)
```

### 2. OpenAPI接口设计

**参数定义模板**:
```php
class miele_openapi_params_v1_[module] {
    public function getAppParams($method) {
        return [
            'addCallback' => [
                'otherSystemSO' => [
                    'type' => 'string',
                    'required' => 'true',
                    'name' => '业务单号',
                    'desc' => '系统内部单号'
                ],
                'sapSystemSO' => [
                    'type' => 'string', 
                    'required' => 'true',
                    'name' => 'SAP单号',
                    'desc' => 'SAP系统生成的单号'
                ]
            ]
        ];
    }
}
```

### 3. 定时任务设计

**定时任务模板**:
```php
class miele_autotask_timer_push[entity] {
    public function exec() {
        try {
            // 1. 获取待处理数据
            $entities = $this->_getPendingEntities();
            
            // 2. 批量处理
            foreach ($entities as $entity) {
                $this->_processEntity($entity);
            }
            
            // 3. 记录执行日志
            $this->_logExecution(count($entities));
            
        } catch (Exception $e) {
            kernel::log('定时任务执行失败: ' . $e->getMessage());
        }
    }
}
```

## 常见问题解决方案

### 1. SAP接口超时处理

```php
// 设置合理的超时时间
$timeout = 30; // 秒

// 实现重试机制
$maxRetries = 3;
for ($i = 0; $i < $maxRetries; $i++) {
    $result = $this->_callSapApi($data);
    if ($result['success']) break;
    sleep(1); // 等待1秒后重试
}
```

### 2. 并发操作处理

```php
// 使用数据库锁防止并发
$db = kernel::database();
$db->beginTransaction();

try {
    // 加锁查询
    $entity = $model->db_dump(['id' => $id], '*', 'FOR UPDATE');
    
    // 状态检查
    if ($entity['status'] != 'expected_status') {
        throw new Exception('状态已变更');
    }
    
    // 业务处理
    $this->_processBusinessLogic($entity);
    
    $db->commit();
} catch (Exception $e) {
    $db->rollBack();
    throw $e;
}
```

### 3. 错误日志记录

```php
// 统一的错误日志格式
kernel::log(sprintf(
    '[%s] %s失败: %s, 参数: %s',
    get_class($this),
    $operation,
    $error,
    json_encode($params)
));

// 操作日志记录
$opObj = app::get('ome')->model('operation_log');
$opObj->write_log(
    $operation . '@miele',
    $entityId,
    $description
);
```

## 性能优化建议

### 1. 数据库优化

- 为状态字段添加索引
- 使用批量操作减少数据库访问
- 定期清理历史日志数据

### 2. 接口优化

- 实现接口缓存机制
- 使用连接池复用连接
- 设置合理的超时时间

### 3. 前端优化

- 使用Tab分页减少数据加载
- 实现异步操作提升用户体验
- 添加操作确认避免误操作

## 测试策略

### 1. 单元测试重点

- 业务逻辑验证
- 状态流转测试
- 异常处理测试

### 2. 集成测试重点

- SAP接口调用
- 回调处理验证
- 端到端流程测试

### 3. 性能测试重点

- 并发操作测试
- 大数据量处理
- 接口响应时间

## 部署检查清单

- [ ] 数据库表结构更新
- [ ] 配置文件部署
- [ ] 定时任务配置
- [ ] OpenAPI接口注册
- [ ] 权限配置更新
- [ ] 日志目录创建
- [ ] 监控配置部署

## 扩展指南

本设计模式可扩展到其他SAP集成场景：
- 订单对接SAP
- 库存同步SAP  
- 财务数据对接SAP
- 客户主数据同步

只需替换相应的业务实体和流程逻辑，保持架构模式不变。
