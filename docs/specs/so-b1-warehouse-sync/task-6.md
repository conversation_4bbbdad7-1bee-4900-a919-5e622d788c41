# Task 6: 部署配置和文档完善

## 任务概述
完成功能部署配置，编写用户文档和运维文档，确保功能可以顺利上线和维护。

## 详细设计

### 部署配置

#### 1. 数据库配置检查
确保相关数据表和字段存在：

```sql
-- 检查sales_material表
DESCRIBE sales_material;

-- 检查customer_classify表  
DESCRIBE customer_classify;

-- 检查sap_so_items表的branch_id字段
DESCRIBE sap_so_items;

-- 如果branch_id字段不存在，需要添加
ALTER TABLE sap_so_items ADD COLUMN branch_id INT(11) DEFAULT NULL;
```

#### 2. 权限配置
确保应用有足够的数据库权限：

```sql
-- 确保miele应用可以访问material应用的表
GRANT SELECT ON material.sales_material TO 'miele_user'@'%';
GRANT SELECT ON material.customer_classify TO 'miele_user'@'%';
```

#### 3. 配置文件更新
检查应用配置文件中的相关设置：

```php
// config/miele.php
return [
    'b1_warehouse_sync' => [
        'enabled' => true,
        'default_branch_timeout' => 5000, // 5秒超时
        'esb_sync_enabled' => true
    ]
];
```

### 用户文档

#### 1. 功能说明文档

**B1商品仓库自动分配功能说明**

**功能概述**
- 系统会自动为B1类商品分配默认仓库
- 在订单创建和更新时自动执行
- 大家电B1商品在ESB同步时会包含仓库信息

**使用说明**
1. 创建包含B1商品的订单时，系统自动分配仓库
2. 更新订单时，未分配仓库的B1商品会自动分配
3. ESB同步时，大家电B1商品会包含plant和storageLocation字段

**注意事项**
- 只有B1类商品会自动分配仓库
- 已分配仓库的商品不会重复分配
- 仓库分配失败不会影响订单创建/更新

#### 2. 故障排查文档

**常见问题及解决方案**

**问题1：B1商品没有自动分配仓库**
- 检查商品是否正确分类为B1
- 检查默认仓库配置是否正确
- 查看日志中的错误信息

**问题2：ESB同步缺少仓库信息**
- 确认商品是否为大家电B1品
- 检查商品是否已分配仓库
- 验证getBranchParams方法返回值

**问题3：性能问题**
- 检查数据库查询性能
- 监控订单创建时间
- 优化批量处理逻辑

### 运维文档

#### 1. 监控指标

**业务指标**
- B1商品仓库分配成功率
- 订单创建/更新平均耗时
- ESB同步成功率

**技术指标**
- 数据库查询响应时间
- 内存使用情况
- 错误日志数量

#### 2. 日志监控

**关键日志关键词**
- `B1Product_Check`: B1商品判断相关
- `assignWarehouseForB1Products`: 仓库分配相关
- `ESB_Sync`: ESB同步相关

**日志查询示例**
```bash
# 查看B1商品判断日志
grep "B1Product_Check" /var/log/miele/app.log

# 查看仓库分配错误
grep "assignWarehouseForB1Products.*error" /var/log/miele/app.log

# 查看ESB同步仓库信息
grep "ESB_Sync.*warehouse" /var/log/miele/app.log
```

#### 3. 性能监控

**数据库查询监控**
```sql
-- 监控B1商品查询性能
SELECT * FROM information_schema.processlist 
WHERE info LIKE '%sales_material%' OR info LIKE '%customer_classify%';

-- 监控仓库分配更新性能
SELECT * FROM information_schema.processlist 
WHERE info LIKE '%sap_so_items%' AND info LIKE '%branch_id%';
```

**应用性能监控**
- 订单创建时间对比（启用前后）
- 内存使用情况监控
- CPU使用率监控

### 回滚方案

#### 1. 功能回滚
如果发现问题需要回滚：

```php
// 在配置文件中禁用功能
'b1_warehouse_sync' => [
    'enabled' => false,
    // 其他配置保持不变
];
```

#### 2. 数据回滚
如果需要清除已分配的仓库：

```sql
-- 清除B1商品的仓库分配（谨慎操作）
UPDATE sap_so_items 
SET branch_id = NULL 
WHERE sales_material_bn IN (
    SELECT sm.sales_material_bn 
    FROM sales_material sm 
    JOIN customer_classify cc ON sm.class_id = cc.class_id 
    WHERE cc.class_name = 'B1'
);
```

### 验收标准

#### 部署验收
- [ ] 数据库表结构正确
- [ ] 权限配置完整
- [ ] 配置文件更新

#### 文档验收
- [ ] 用户文档完整清晰
- [ ] 故障排查文档实用
- [ ] 运维文档详细

#### 监控验收
- [ ] 监控指标配置完成
- [ ] 日志监控正常
- [ ] 告警机制有效

---

## 实施步骤

### Step 1: 环境准备
1. 检查数据库表结构
2. 配置权限和参数
3. 更新配置文件

### Step 2: 文档编写
1. 编写用户使用文档
2. 编写故障排查文档
3. 编写运维监控文档

### Step 3: 监控配置
1. 配置业务监控指标
2. 设置日志监控规则
3. 建立告警机制

### Step 4: 上线验证
1. 在测试环境验证完整流程
2. 在生产环境小范围验证
3. 全量上线并监控

---

## 相关文档
- **需求文档**: [requirements.md](requirements.md) - 所有需求
- **设计文档**: [design.md](design.md) - 所有组件
- **Task 1-5**: 所有功能实现任务
