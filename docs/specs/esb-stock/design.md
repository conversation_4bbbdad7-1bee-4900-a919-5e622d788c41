# SAP库存同步重构技术设计文档

> **核心原则**: 本文档旨在提供一个清晰、可执行的设计蓝图。所有设计决策都应直接、明确，并包含足够的细节以生成后续的任务清单。不考虑数据迁移和向后兼容性。

## 1. 设计概述

- **设计目标**: 重构SAP库存同步系统，提供统一的`syncStock`方法专注于ESB库存数据获取和存储，独立的`generateAdjustOrders`方法处理库存调整单生成，支持定时任务自动调用和手动选择性调用
- **技术选型**: PHP 7.4+, MySQL 5.7+, Redis缓存, ESB接口服务
- **架构模式**: 分层架构，服务导向设计，基于现有的dbeav框架

---

## 2. 核心问题与设计决策

- **Q1: 为什么选择统一的syncStock方法而不是分别实现定时任务和控制器调用？**
  - **A:** 统一入口可以确保业务逻辑的一致性，减少代码重复，便于维护和测试。定时任务和控制器只需要处理参数准备和结果处理，核心同步逻辑完全复用。

- **Q2: 为什么需要批次号管理机制？**
  - **A:** 批次号用于标识每次同步操作，便于追踪同步状态、错误排查和数据回滚。同时ESB接口要求每次请求都有唯一标识。

- **Q3: 为什么要在同步前更新历史数据状态？**
  - **A:** 防止历史未处理的数据与新同步的数据产生冲突，确保每次同步都是基于最新状态的完整同步。

- **Q4: 为什么使用branch_bn字段作为仓库编码？**
  - **A:** branch_bn字段包含了SAP系统需要的"工厂编码:货位编码"格式，可以直接解析使用。这是业务约定的标准格式。

- **Q5: 为什么syncStock方法不直接生成库存调整单？**
  - **A:** 职责分离原则。syncStock专注于ESB库存数据获取和存储，库存调整单生成由独立的generateAdjustOrders方法处理。这样可以支持不同的调用场景：定时任务自动生成调整单，手动操作可选择性生成调整单。

---

## 3. 核心设计方案

### **文件结构变更**

- **说明**: 基于现有代码结构，主要涉及库存同步核心服务、定时任务、控制器和相关模型的重构
- **结构图**:
  ```
  app/miele/
  ├── lib/
  │   ├── stock/
  │   │   ├── sync.php              # 核心同步服务（已存在，需重构）
  │   │   └── adjust.php            # 库存调整服务（已存在）
  │   ├── esb/
  │   │   └── stock.php             # ESB库存接口（已存在）
  │   ├── autotask/timer/
  │   │   └── stocksync.php         # 定时任务（已存在，需重构）
  │   └── operation/
  │       └── log.php               # 操作日志定义（已存在，需扩展）
  ├── controller/admin/stock/
  │   ├── masterdata.php            # 白名单控制器（已存在，需重构）
  │   └── sync.php                  # 库存同步记录控制器（已存在，需扩展）
  ├── model/
  │   ├── stock.php                 # 库存模型（已存在，需扩展）
  │   └── stock/
  │       └── masterdata.php        # 白名单模型（已存在）
  └── dbschema/
      ├── stock.php                 # 库存表结构（已存在）
      └── stock_masterdata.php      # 白名单表结构（已存在）
  ```

### **数据库 Schema 定义**

- **说明**: 基于现有的数据库表结构，重点关注状态字段和索引优化
- **`dbschema/stock.php`**:
  ```php
  $db['stock'] = array(
      'columns' => array(
          'id' => array('type' => 'int unsigned', 'pkey' => true, 'extra' => 'auto_increment'),
          'batch_no' => array('type' => 'varchar(50)', 'required' => true, 'label' => '批次号'),
          'bm_id' => array('type' => 'int unsigned', 'required' => true, 'label' => '基础物料ID'),
          'material_bn' => array('type' => 'varchar(200)', 'required' => true, 'label' => '物料编码'),
          'branch_id' => array('type' => 'int unsigned', 'required' => true, 'label' => '仓库ID'),
          'branch_bn' => array('type' => 'varchar(200)', 'required' => true, 'label' => '仓库编码'),
          'available_stock' => array('type' => 'int', 'default' => 0, 'label' => 'SAP可用库存'),
          'actual_stock' => array('type' => 'int', 'default' => 0, 'label' => '实际库存'),
          'frozen_stock' => array('type' => 'int', 'default' => 0, 'label' => 'OMS冻结库存'),
          'sync_status' => array(
              'type' => array('pending' => '待处理', 'processing' => '处理中', 'success' => '成功', 'failed' => '失败', 'none' => '无需同步'),
              'default' => 'pending'
          ),
          'sync_msg' => array('type' => 'text', 'label' => '同步消息'),
      ),
      'index' => array(
          'ind_batch_material_branch' => array(
              'columns' => array('batch_no', 'material_bn', 'branch_bn'),
              'prefix' => 'UNIQUE'
          ),
          'ind_sync_status' => array('columns' => array('sync_status')),
      ),
  );
  ```

### **核心代码实现**

- **说明**: 基于现有代码结构，重点描述核心方法的实现逻辑
- **`lib/stock/sync.php`**:
  - `syncStock($materialBns, $options = [])`: 核心同步方法入口（只负责ESB数据获取和存储）
  - `_getValidBranches($options = [])`: 获取有效发货仓库列表，使用`branch_bn`字段验证格式
  - `_validateBranchCode($branchCode)`: 验证仓库编码格式（验证`branch_bn`是否为"工厂编码:货位编码"格式）
  - `_updateHistoryBatchStatus($currentBatchNo, $options = [])`: 更新历史数据状态
  - `_syncBranchStock($branch, $materialBns, $batchNo, $options = [])`: 单个仓库库存同步
- **`lib/stock/adjust.php`**:
  - `generateAdjustOrders($batchNo)`: 根据批次号生成库存调整单
- **关键代码片段**:
  ```php
  // lib/stock/sync.php: _getValidBranches() 方法（使用branch_bn验证格式）
  private function _getValidBranches($options = []) {
      $branches = $this->branchModel->getList('branch_id,branch_bn,name', [
          'is_deliv_branch' => 'true',
          'type' => 'main',
          'disabled' => 'false'
      ]);

      // 过滤编码格式不正确的仓库（验证branch_bn格式）
      $validBranches = [];
      foreach ($branches as $branch) {
          if ($this->_validateBranchCode($branch['branch_bn'])) {
              $validBranches[] = $branch;
          }
      }
      return $validBranches;
  }

  // 验证branch_bn是否为"工厂编码:货位编码"格式
  private function _validateBranchCode($branchCode) {
      if (empty($branchCode)) return false;
      $parts = explode(':', $branchCode);
      return count($parts) === 2 && !empty(trim($parts[0])) && !empty(trim($parts[1]));
  }
  ```

---

## 4. 核心业务流程设计

### **syncStock 方法执行流程**

```mermaid
graph TD
    A[开始 syncStock] --> B[参数验证]
    B --> C[获取并发锁]
    C --> D[生成批次号]
    D --> E[验证批次号唯一性]
    E --> F[更新历史数据状态]
    F --> G[获取有效发货仓库]
    G --> H[循环处理每个仓库]
    H --> I[验证仓库编码格式]
    I --> J[解析仓库编码]
    J --> K[调用ESB接口]
    K --> L[处理返回数据]
    L --> M[保存到sdb_miele_stock表]
    M --> N{还有仓库?}
    N -->|是| H
    N -->|否| O[释放并发锁]
    O --> P[返回结果]
```

### **批次号生成机制**

- **实现方式**: 基于`eccommon_guid`服务的`incId`方法
- **格式**: `STOCK + YYYYMMDD + 6位递增序号`
- **示例**: `STOCK202501201000001`
- **唯一性保证**: 通过数据库查询验证批次号不重复

### **仓库筛选逻辑**

- **筛选条件**:
  ```php
  [
      'is_deliv_branch' => 'true',  // 发货仓库
      'type' => 'main',             // 主仓
      'disabled' => 'false'         // 启用状态
  ]
  ```
- **编码验证**: 使用`branch_bn`字段，格式必须为"工厂编码:货位编码"
- **验证逻辑**: `explode(':', $branchBn)`后必须有且仅有2个非空部分

### **历史数据状态管理**

- **更新时机**: 在ESB接口调用前执行
- **更新范围**: 指定物料和仓库组合的历史记录
- **更新条件**: 只更新状态为`pending`和`failed`的记录
- **保留条件**: 状态为`success`的记录保持不变
- **排除条件**: 排除当前批次号的记录

### **ESB接口调用设计**

- **接口方法**: `miele.qimen.esb.stock.query`
- **请求参数格式**:
  ```php
  [
      'system' => 'OMS',
      'plant' => '9601',              // 从branch_bn解析的工厂编码
      'storageLocation' => '1000',    // 从branch_bn解析的货位编码
      'item' => [
          ['material' => 'MAT001'],
          ['material' => 'MAT002'],
          // ...
      ]
  ]
  ```
- **返回数据处理**:
  ```php
  // ESB返回格式转换为内部格式
  foreach ($response['data']['item'] as $item) {
      $stockData[] = [
          'material_code' => $item['material'],
          'warehouse_code' => $plant . ':' . $storageLocation,
          'available_stock' => intval(floatval($item['availableQuan'] ?? 0)),
          'plant' => $plant,
      ];
  }
  ```

### **库存数据计算逻辑**

- **SAP可用库存**: 直接从ESB接口返回的`availableQuan`字段获取
- **OMS冻结库存**: 查询OMS系统中该物料仓库的冻结库存数量
- **实际库存计算**: `实际库存 = SAP可用库存 + OMS冻结库存`
- **数据保存**: 将三个库存值都保存到`sdb_miele_stock`表中

---

## 5. 调用方式设计

### **定时任务调用重构**

- **文件**: `app/miele/lib/autotask/timer/stocksync.php`
- **核心逻辑**:
  ```php
  public function process($params, &$error_msg = '') {
      // 1. 防并发处理
      if (!$this->acquireLock(self::LOCK_KEY, self::LOCK_TIMEOUT)) {
          return true; // 跳过本次执行
      }

      // 2. 获取白名单物料编码
      $materials = $this->stockMasterdataModel->getList('material_bn');
      $materialBns = array_column($materials, 'material_bn');

      // 3. 分批处理（每批100个物料）
      $batches = array_chunk($materialBns, 100);
      foreach ($batches as $batchIndex => $batch) {
          $syncService = kernel::single('miele_stock_sync');
          list($success, $message, $data) = $syncService->syncStock($batch, [
              'sync_mode' => 'timer',
              'batch_index' => $batchIndex
          ]);

          // 4. 生成库存调整单（定时任务自动生成）
          if ($success && isset($data['batch_no'])) {
              $adjustService = kernel::single('miele_stock_adjust');
              list($adjustSuccess, $adjustMessage) = $adjustService->generateAdjustOrders($data['batch_no']);
              if (!$adjustSuccess) {
                  kernel::log("定时任务调整单生成失败：" . $adjustMessage, 'warning');
              }
          }
      }
  }
  ```

### **控制器手动调用重构**

- **文件**: `app/miele/controller/admin/stock/masterdata.php`
- **核心逻辑**:
  ```php
  public function sync_stock() {
      // 1. 参数验证
      $materialIds = $_POST['id'];
      if (empty($materialIds)) {
          $this->end(false, '请选择要同步的物料');
      }

      // 2. 获取物料编码
      $model = app::get('miele')->model('stock_masterdata');
      $materials = $model->getList('material_bn', ['id' => $materialIds]);
      $materialBns = array_column($materials, 'material_bn');

      // 3. 调用同步服务（只同步数据，不生成调整单）
      $syncService = kernel::single('miele_stock_sync');
      list($success, $message, $data) = $syncService->syncStock($materialBns, [
          'sync_mode' => 'manual',
          'operator_id' => $this->user->user_id
      ]);

      // 4. 记录操作日志（使用有意义的主键）
      if ($success) {
          $opObj = app::get('ome')->model('operation_log');
          // 为每个选中的物料记录操作日志
          foreach ($materialIds as $materialId) {
              $opObj->write_log(
                  'stock_masterdata_sync@miele',
                  $materialId, // 使用stock_masterdata表的主键ID
                  "手动同步库存 - 批次号：" . ($data['batch_no'] ?? '未知')
              );
          }
          $message .= "。如需生成库存调整单，请到库存同步记录页面操作。";
      }

      // 5. 返回结果
      $this->end($success, $message);
  }
  ```

### **库存同步记录控制器扩展**

- **文件**: `app/miele/controller/admin/stock/sync.php`
- **扩展功能**: 在"待处理"和"处理失败"状态的tab页面添加"生成库存调整单"按钮
- **核心逻辑**:
  ```php
  /**
   * 生成库存调整单
   */
  public function generate_adjust_orders() {
      $this->begin('index.php?app=miele&ctl=admin_stock_sync&act=index');

      // 1. 获取选中的记录ID并验证
      $stockIds = $_POST['id'];
      if (empty($stockIds)) {
          $this->end(false, '请选择要生成调整单的记录');
      }

      // 2. 获取批次号（按批次号分组处理）
      $stockModel = app::get('miele')->model('stock');
      $stocks = $stockModel->getList('batch_no', ['id' => $stockIds, 'sync_status|in' => ['pending', 'failed']]);
      $batchNos = array_unique(array_column($stocks, 'batch_no'));

      // 3. 为每个批次生成调整单
      $adjustService = kernel::single('miele_stock_adjust');
      $results = [];
      foreach ($batchNos as $batchNo) {
          list($success, $message, $data) = $adjustService->generateAdjustOrders($batchNo);
          $processedCount = $data['processed_count'] ?? 0;
          $results[] = "批次 {$batchNo}: " . ($success ? "成功处理 {$processedCount} 条记录" : "失败 - {$message}");
      }

      // 4. 返回结果（不记录操作日志，状态已在数据库表中记录）
      $this->end(true, "调整单生成完成:\n" . implode("\n", $results));
  }
  ```

---

## 6. 错误处理与日志设计

### **异常处理策略**

- **并发控制异常**: 当检测到同步正在进行时，返回友好提示而不是错误
- **参数验证异常**: 对输入参数进行严格验证，返回具体的错误信息
- **业务逻辑异常**: 如批次号重复、仓库编码格式错误等，记录警告日志
- **系统异常**: 如数据库连接失败、ESB接口异常等，记录错误日志
- **部分失败处理**: 单个仓库同步失败不影响其他仓库的同步

### **操作日志设计**

- **日志记录原则**: 严格按照`docs/cheatsheet/operation-log-cheatsheet.md`规范，操作日志必须有有意义的主键
- **记录范围**:
  - ✅ 手动触发库存同步：记录到`stock_masterdata`表的主键ID
  - ❌ 库存调整单生成不需要记录操作日志（状态已在数据库表中记录）
  - ❌ 不记录具体的库存数据变更（已在`sdb_miele_stock`表中记录状态）
- **ESB接口日志**: 继续使用现有的`api_log`机制，无需额外处理

### **并发控制设计**

- **锁机制**: 使用Redis分布式锁防止重复同步
- **锁超时**: 设置5分钟的锁超时时间
- **锁粒度**: 全局锁，确保同一时间只有一个同步任务在执行
- **锁释放**: 无论成功失败都要确保锁的正确释放

---

## 7. 库存调整单生成设计

### **调整单生成流程**

- **触发时机**:
  - 定时任务：在`syncStock`方法成功完成后自动调用
  - 手动操作：在库存同步记录页面选择性调用
- **数据来源**: `sdb_miele_stock`表中状态为`pending`或`failed`的记录
- **生成逻辑**:
  ```php
  public function generateAdjustOrders($batchNo) {
      // 1. 获取待处理的库存数据（包括失败的记录）
      $stockItems = $this->stockModel->getPendingStocks($batchNo, ['pending', 'failed']);

      // 2. 按仓库分组
      $groupedByBranch = [];
      foreach ($stockItems as $item) {
          $groupedByBranch[$item['branch_id']][] = $item;
      }

      // 3. 为每个仓库生成调整单
      foreach ($groupedByBranch as $branchId => $items) {
          $this->_generateSingleAdjustOrder($branchId, $items);
      }

      return [true, "调整单生成完成", ['processed_count' => count($stockItems)]];
  }
  ```

### **调用场景对比**

| 调用方式 | syncStock | generateAdjustOrders | 说明 |
|---------|-----------|---------------------|------|
| 定时任务 | 自动调用 | 自动调用 | 完全自动化流程 |
| 手动拉取 | 手动调用 | 不调用 | 只同步数据，不生成调整单 |
| 手动生成调整单 | 不调用 | 手动调用 | 针对已同步的数据生成调整单 |

### **状态更新机制**

- **状态记录**: 所有库存同步和调整单生成的状态都记录在`sdb_miele_stock`表的`sync_status`字段中
- **成功状态**: 调整单生成成功后，将对应记录状态更新为`success`
- **失败状态**: 调整单生成失败时，将对应记录状态更新为`failed`
- **批量更新**: 使用事务确保状态更新的原子性
- **无需重复日志**: 状态变更已在数据库表中记录，无需额外的操作日志

---

## 8. 安全设计

### **安全控制**

- **权限验证**: 控制器调用需要验证用户权限
- **参数验证**: 严格验证所有输入参数
- **SQL注入防护**: 使用参数化查询防止SQL注入
- **并发安全**: 使用分布式锁防止并发执行导致的数据冲突

---

## 9. 参考资料

- **参考的 Cheatsheet**: 无
- **参考的 Reference 文档**: 无
- **参考的 URL**: 无

---

## 10. 相关测试用例

- 批次号生成和唯一性验证测试
- 仓库编码格式验证测试（正确格式和错误格式）
- ESB接口调用成功和失败场景测试
- 历史数据状态更新测试
- 并发同步控制测试
- 定时任务完整流程测试
- 控制器手动调用测试
- 库存调整单生成测试

---

## 11. 产出评估

- **是否需要生成新的 Cheatsheet?**: 否
  - **理由**: 本次重构主要是对现有库存同步功能的优化和重构，没有引入新的通用设计模式或技术方案。

- **是否需要生成 Reference 文档?**: 否
  - **理由**: 本次设计主要针对特定的业务需求，不具有长期参考价值，且相关的技术细节已在现有代码中体现。

---
