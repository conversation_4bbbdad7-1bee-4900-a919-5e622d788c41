---
# 里程碑1: 核心库存同步服务重构
# 状态: 待处理
# 源任务: docs/specs/SAP库存同步重构/tasks.md 中的里程碑1
---

> **核心原则**: 本文档是 AI 的精确执行手册。每个任务都应明确、原子化，并包含清晰的 `执行` 和 `验证` 步骤，确保操作的准确性和可追溯性。

---

- [ ] **任务 1.1: 验证核心库存同步服务当前架构** `[待处理]`
  - **目标文件**: `app/miele/lib/stock/sync.php`
  - **操作**: `检查`
  - **执行**:
    - **查看当前syncStock方法实现**:
      ```shell
      grep -A 30 "public function syncStock" app/miele/lib/stock/sync.php
      ```
    - **确认当前职责范围**:
      ```shell
      grep -n "generateAdjustOrders\|调整单" app/miele/lib/stock/sync.php
      ```
  - **验证**:
    ```shell
    php -l app/miele/lib/stock/sync.php && echo "语法检查通过"
    ```

---

- [ ] **任务 1.2: 分析仓库编码字段使用情况** `[待处理]`
  - **目标文件**: `app/miele/lib/stock/sync.php`
  - **操作**: `分析`
  - **执行**:
    - **查看storage_code使用情况**:
      ```shell
      grep -n "storage_code" app/miele/lib/stock/sync.php
      ```
    - **查看branch_bn使用情况**:
      ```shell
      grep -n "branch_bn" app/miele/lib/stock/sync.php
      ```
    - **确认设计要求**:
      ```shell
      echo "设计要求：使用branch_bn字段，格式为'工厂编码:货位编码'"
      ```
  - **验证**:
    ```shell
    echo "字段使用分析完成"
    ```

---

- [ ] **任务 1.3: 验证批次号生成机制** `[待处理]`
  - **目标文件**: `app/miele/model/stock.php`
  - **操作**: `检查`
  - **执行**:
    - **查看generateBatchNo方法**:
      ```shell
      grep -A 10 "generateBatchNo" app/miele/model/stock.php
      ```
    - **确认批次号格式要求**: STOCK + YYYYMMDD + 6位递增序号
    - **确认使用eccommon_guid服务**:
      ```shell
      grep -B 5 -A 5 "eccommon_guid" app/miele/model/stock.php
      ```
  - **验证**:
    ```shell
    php -l app/miele/model/stock.php && echo "语法检查通过"
    ```

---

- [ ] **任务 1.4: 验证仓库编码验证和解析逻辑** `[待处理]`
  - **目标文件**: `app/miele/lib/stock/sync.php`
  - **操作**: `检查`
  - **执行**:
    - **查看_validateBranchCode方法**:
      ```shell
      grep -A 10 "_validateBranchCode" app/miele/lib/stock/sync.php
      ```
    - **查看_parseBranchCode方法**:
      ```shell
      grep -A 10 "_parseBranchCode" app/miele/lib/stock/sync.php
      ```
    - **确认格式要求**: "工厂编码:货位编码"格式验证和解析
  - **验证**:
    ```shell
    php -l app/miele/lib/stock/sync.php && echo "语法检查通过"
    ```

---

- [ ] **任务 1.5: 验证ESB接口调用和数据处理流程** `[待处理]`
  - **目标文件**: `app/miele/lib/stock/sync.php`
  - **操作**: `检查`
  - **执行**:
    - **查看ESB接口调用逻辑**:
      ```shell
      grep -B 5 -A 15 "miele.qimen.esb.stock.query" app/miele/lib/stock/sync.php
      ```
    - **确认数据处理流程**:
      ```shell
      grep -A 20 "_syncBranchStock\|_syncStockForBranch" app/miele/lib/stock/sync.php
      ```
  - **验证**:
    ```shell
    php -l app/miele/lib/stock/sync.php && echo "语法检查通过"
    ```

---

- [ ] **任务 1.6: 确认职责分离设计符合要求** `[待处理]`
  - **目标文件**: `app/miele/lib/stock/sync.php`
  - **操作**: `确认`
  - **执行**:
    - **确认syncStock方法职责**:
      ```shell
      echo "syncStock方法应该只负责：ESB数据获取、存储到sdb_miele_stock表、返回批次号"
      ```
    - **确认不包含调整单生成**:
      ```shell
      echo "syncStock方法不应该调用generateAdjustOrders，职责分离"
      ```
  - **验证**:
    ```shell
    echo "职责分离设计确认完成"
    ```

---
