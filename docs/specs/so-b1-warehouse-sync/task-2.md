# Task 2: 增强create_order方法集成B1商品仓库分配

## 任务概述
在 `app/miele/lib/order.php` 的 `create_order` 方法中集成B1商品仓库自动分配逻辑。

## 详细设计

### 文件位置
- **目标文件**: `app/miele/lib/order.php`
- **修改方法**: `create_order($orderInfo)`
- **新增私有方法**: `assignWarehouseForB1Products($orderInfo)`

### 核心实现逻辑

#### 1. 修改create_order主方法

```php
public function create_order($orderInfo) {
    // 原有订单创建逻辑保持不变...
    // [原有代码不做修改]

    // 新增：B1商品仓库分配逻辑
    $this->assignWarehouseForB1Products($orderInfo);

    // 继续原有的订单创建逻辑...
    // [原有代码不做修改]
}
```

#### 2. 实现B1商品仓库分配核心方法

```php
/**
 * 为B1商品分配默认仓库
 * @param array $orderInfo 订单信息
 */
private function assignWarehouseForB1Products($orderInfo) {
    if (empty($orderInfo['order_id']) || empty($orderInfo['so_id'])) {
        return;
    }

    $orderId = $orderInfo['order_id'];
    $soId = $orderInfo['so_id'];

    // 获取订单商品信息
    $soItemsMdl = app::get('miele')->model('sap_so_items');
    $soItems = $soItemsMdl->getList('*', ['so_id' => $soId]);

    if (empty($soItems)) {
        return;
    }

    // 遍历处理每个商品
    foreach ($soItems as $item) {
        if (empty($item['sales_material_bn']) || !empty($item['branch_id'])) {
            continue;
        }

        // 判断是否为B1商品
        if (miele_func::isB1Product($item['sales_material_bn'])) {
            // 获取默认仓库
            $branchId = miele_func::getDefaultBranch($orderId);

            if ($branchId) {
                // 更新仓库信息
                $soItemsMdl->update(['branch_id' => $branchId], ['id' => $item['id']]);
            }
        }
    }
}
```

### 测试用例设计

#### 正常流程测试
1. **B1商品自动分配仓库**: 验证B1商品获得默认仓库
2. **非B1商品不处理**: 验证非B1商品不受影响
3. **已分配仓库不重复**: 验证已有仓库的商品不重复分配

#### 异常情况测试
1. **订单参数缺失**: 验证参数验证逻辑
2. **商品数据异常**: 验证数据完整性检查
3. **仓库获取失败**: 验证降级处理

### 验收标准

#### 功能验收
- [ ] B1商品在订单创建时自动分配默认仓库
- [ ] 非B1商品不受影响
- [ ] 已分配仓库的B1商品不重复分配

---

## 实施步骤

### Step 1: 代码实现
1. 修改 `create_order` 方法添加B1处理调用
2. 实现 `assignWarehouseForB1Products` 方法

### Step 2: 测试验证
1. 测试B1商品仓库分配功能
2. 验证异常情况处理

---

## 相关文档
- **需求文档**: [requirements.md](requirements.md) - Requirement 2
- **设计文档**: [design.md](design.md) - Component 2
- **Task 1**: [task-1.md](task-1.md) - 依赖的B1商品判断逻辑
