---
# 里程碑3: 控制器功能扩展
# 状态: 待处理
# 源任务: docs/specs/SAP库存同步重构/tasks.md 中的里程碑3
---

> **核心原则**: 本文档是 AI 的精确执行手册。每个任务都应明确、原子化，并包含清晰的 `执行` 和 `验证` 步骤，确保操作的准确性和可追溯性。

---

- [ ] **任务 3.1: 验证库存同步记录控制器当前状态** `[待处理]`
  - **目标文件**: `app/miele/controller/admin/stock/sync.php`
  - **操作**: `检查`
  - **执行**:
    - **查看当前控制器结构**:
      ```shell
      grep -n "class\|function" app/miele/controller/admin/stock/sync.php
      ```
    - **查看index方法实现**:
      ```shell
      grep -A 20 "public function index" app/miele/controller/admin/stock/sync.php
      ```
  - **验证**:
    ```shell
    php -l app/miele/controller/admin/stock/sync.php && echo "语法检查通过"
    ```

---

- [ ] **任务 3.2: 分析按钮扩展需求** `[待处理]`
  - **目标文件**: `app/miele/controller/admin/stock/sync.php`
  - **操作**: `分析`
  - **执行**:
    - **查看_views方法中的tab定义**:
      ```shell
      grep -A 10 "_views\|sub_menu" app/miele/controller/admin/stock/sync.php
      ```
    - **确认需要在哪些状态显示按钮**:
      ```shell
      echo "需要在'待处理'(view=1)和'失败'(view=4)状态显示生成调整单按钮"
      ```
  - **验证**:
    ```shell
    echo "按钮扩展需求分析完成"
    ```

---

- [ ] **任务 3.3: 设计generate_adjust_orders方法** `[待处理]`
  - **目标文件**: `app/miele/controller/admin/stock/sync.php`
  - **操作**: `设计`
  - **执行**:
    - **参考现有控制器方法结构**:
      ```shell
      grep -A 20 "public function" app/miele/controller/admin/stock/masterdata.php | head -30
      ```
    - **确认方法需要的功能**:
      ```shell
      echo "需要实现：权限验证、参数检查、批次分组、调用generateAdjustOrders、返回结果"
      ```
  - **验证**:
    ```shell
    echo "方法设计完成"
    ```

---

- [ ] **任务 3.4: 验证权限配置需求** `[待处理]`
  - **目标文件**: `app/miele/controller/admin/stock/sync.php`
  - **操作**: `检查`
  - **执行**:
    - **查看现有权限使用**:
      ```shell
      grep -r "miele_stock.*permission" app/miele/controller/admin/stock/
      ```
    - **确认需要的权限名称**:
      ```shell
      echo "需要使用权限：miele_stock_adjust_generate"
      ```
  - **验证**:
    ```shell
    echo "权限配置需求确认完成"
    ```

---
