# 退货对接SAP - 部署指南

## 概述

本文档描述了Miele退货单与SAP系统对接功能的部署流程、配置要求和监控设置。

## 部署前准备

### 1. 环境要求

#### 服务器环境
- **操作系统**: Linux (CentOS 7+ / Ubuntu 18+)
- **PHP版本**: PHP 7.4+
- **数据库**: MySQL 5.7+ / MariaDB 10.3+
- **Web服务器**: Nginx 1.16+ / Apache 2.4+
- **内存**: 最低4GB，推荐8GB
- **磁盘**: 最低50GB可用空间

#### 网络要求
- **SAP系统连接**: 确保服务器能访问SAP系统接口
- **防火墙配置**: 开放必要的端口（80, 443, 3306等）
- **SSL证书**: 生产环境必须配置HTTPS

### 2. 依赖检查

#### PHP扩展
```bash
# 检查必需的PHP扩展
php -m | grep -E "(curl|json|pdo|pdo_mysql|openssl|mbstring)"
```

#### 数据库权限
```sql
-- 确保数据库用户具有以下权限
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, ALTER, INDEX ON miele.* TO 'miele_user'@'%';
```

## 部署步骤

### 1. 代码部署

#### 1.1 备份现有代码
```bash
# 备份当前版本
cd /var/www/html
tar -czf backup_$(date +%Y%m%d_%H%M%S).tar.gz miele/
```

#### 1.2 部署新代码
```bash
# 部署新文件
cp -r new_files/* /var/www/html/miele/
chown -R www-data:www-data /var/www/html/miele/
chmod -R 755 /var/www/html/miele/
```

### 2. 数据库更新

#### 2.1 执行数据库迁移
```sql
-- 添加新字段到reship_wo表
ALTER TABLE `miele_reship_wo` 
ADD COLUMN `sap_sync_time` INT(11) DEFAULT NULL COMMENT 'SAP同步时间',
ADD COLUMN `bucha_status` VARCHAR(20) DEFAULT 'none' COMMENT '补差状态',
ADD COLUMN `sap_reship_bn` VARCHAR(50) DEFAULT NULL COMMENT 'SAP退货单号',
ADD COLUMN `sap_sync_status` VARCHAR(20) DEFAULT 'none' COMMENT 'SAP同步状态',
ADD COLUMN `contact_name` VARCHAR(100) DEFAULT NULL COMMENT '联系人姓名',
ADD COLUMN `contact_phone` VARCHAR(20) DEFAULT NULL COMMENT '联系电话',
ADD COLUMN `contact_address` VARCHAR(500) DEFAULT NULL COMMENT '联系地址',
ADD COLUMN `logistics_fee_required` TINYINT(1) DEFAULT 0 COMMENT '是否需要物流费用',
ADD COLUMN `logistics_fee` DECIMAL(10,2) DEFAULT 0.00 COMMENT '物流费用';

-- 添加索引
ALTER TABLE `miele_reship_wo` 
ADD INDEX `idx_sap_sync_status` (`sap_sync_status`),
ADD INDEX `idx_sap_sync_time` (`sap_sync_time`),
ADD INDEX `idx_bucha_status` (`bucha_status`);
```

#### 2.2 验证数据库结构
```sql
-- 验证新字段是否添加成功
DESCRIBE miele_reship_wo;
SHOW INDEX FROM miele_reship_wo;
```

### 3. 配置文件更新

#### 3.1 更新服务注册配置
```bash
# 检查services.xml文件
cat /var/www/html/miele/app/miele/services.xml
```

#### 3.2 更新定时任务配置
```bash
# 检查定时任务白名单
cat /var/www/html/miele/app/taskmgr/lib/whitelist/miele.php
```

#### 3.3 配置OpenAPI接口
```bash
# 检查OpenAPI配置
cat /var/www/html/miele/app/miele/lib/openapi/conf.php
```

### 4. 权限配置

#### 4.1 文件权限
```bash
# 设置正确的文件权限
find /var/www/html/miele -type f -exec chmod 644 {} \;
find /var/www/html/miele -type d -exec chmod 755 {} \;
chmod +x /var/www/html/miele/app/miele/lib/autotask/timer/pushreship.php
```

#### 4.2 日志目录权限
```bash
# 确保日志目录可写
mkdir -p /var/log/miele
chown www-data:www-data /var/log/miele
chmod 755 /var/log/miele
```

## 配置说明

### 1. SAP接口配置

#### 1.1 ESB配置
在 `app/miele/lib/esb/syncreship.php` 中配置SAP接口地址：
```php
// SAP接口配置
private $sapApiUrl = 'https://sap-api.miele.com/reship';
private $sapApiKey = 'your-api-key';
private $sapTimeout = 30; // 超时时间（秒）
```

#### 1.2 回调配置
在 `app/miele/lib/esb/callback/reship.php` 中配置回调处理：
```php
// 回调验证配置
private $callbackSecret = 'your-callback-secret';
private $allowedIps = ['*************', '*********']; // SAP服务器IP
```

### 2. 定时任务配置

#### 2.1 Cron配置
```bash
# 添加到crontab
*/5 * * * * /usr/bin/php /var/www/html/miele/app/taskmgr/timer.php pushreship
```

#### 2.2 任务监控
```bash
# 检查定时任务执行日志
tail -f /var/log/miele/pushreship.log
```

### 3. 监控配置

#### 3.1 日志监控
```bash
# 配置日志轮转
cat > /etc/logrotate.d/miele << EOF
/var/log/miele/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
EOF
```

#### 3.2 性能监控
```bash
# 监控脚本示例
#!/bin/bash
# 检查SAP同步状态
mysql -u miele_user -p -e "
SELECT 
    sap_sync_status,
    COUNT(*) as count 
FROM miele_reship_wo 
WHERE DATE(FROM_UNIXTIME(createtime)) = CURDATE() 
GROUP BY sap_sync_status;
"
```

## 验证部署

### 1. 功能验证

#### 1.1 界面访问
```bash
# 检查页面是否正常访问
curl -I http://your-domain.com/miele/admin/reship
```

#### 1.2 API接口验证
```bash
# 测试OpenAPI接口
curl -X POST "http://your-domain.com/openapi/v1/miele.reship.addCallback" \
  -H "Content-Type: application/json" \
  -d '{"otherSystemSO":"TEST001","sapSystemSO":"SAP001"}'
```

### 2. 数据验证

#### 2.1 检查数据库连接
```php
<?php
// 测试数据库连接
try {
    $pdo = new PDO('mysql:host=localhost;dbname=miele', 'miele_user', 'password');
    echo "数据库连接成功\n";
} catch (PDOException $e) {
    echo "数据库连接失败: " . $e->getMessage() . "\n";
}
?>
```

#### 2.2 检查服务可用性
```php
<?php
// 测试服务是否可用
$service = kernel::single('miele_esb_callback_reship');
if ($service) {
    echo "回调服务加载成功\n";
} else {
    echo "回调服务加载失败\n";
}
?>
```

## 回滚方案

### 1. 代码回滚
```bash
# 恢复备份
cd /var/www/html
rm -rf miele/
tar -xzf backup_YYYYMMDD_HHMMSS.tar.gz
chown -R www-data:www-data miele/
```

### 2. 数据库回滚
```sql
-- 如需回滚数据库更改
ALTER TABLE `miele_reship_wo` 
DROP COLUMN `sap_sync_time`,
DROP COLUMN `bucha_status`,
DROP COLUMN `sap_reship_bn`,
DROP COLUMN `sap_sync_status`,
DROP COLUMN `contact_name`,
DROP COLUMN `contact_phone`,
DROP COLUMN `contact_address`,
DROP COLUMN `logistics_fee_required`,
DROP COLUMN `logistics_fee`;
```

## 故障排查

### 1. 常见问题

#### 问题1: SAP同步失败
**症状**: 退货单状态一直为"待同步"
**排查步骤**:
1. 检查网络连接：`ping sap-api.miele.com`
2. 检查API密钥配置
3. 查看错误日志：`tail -f /var/log/miele/error.log`

#### 问题2: 定时任务不执行
**症状**: 定时任务没有按预期执行
**排查步骤**:
1. 检查cron服务：`systemctl status cron`
2. 检查crontab配置：`crontab -l`
3. 查看cron日志：`tail -f /var/log/cron`

### 2. 日志位置
- **应用日志**: `/var/log/miele/application.log`
- **错误日志**: `/var/log/miele/error.log`
- **SAP同步日志**: `/var/log/miele/sap_sync.log`
- **定时任务日志**: `/var/log/miele/pushreship.log`

## 联系方式

如遇部署问题，请联系：
- **技术支持**: <EMAIL>
- **开发团队**: <EMAIL>
- **运维团队**: <EMAIL>
