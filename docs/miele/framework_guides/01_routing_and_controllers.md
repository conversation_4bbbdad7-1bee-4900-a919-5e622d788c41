# 内部框架: 路由与控制器速查表

## 1. 场景与目标

- **解决了什么问题**: 本速查表解释了如何在基于 Shopex 的内部框架中定义新页面或端点，并创建相应的控制器来处理用户请求。
- **最终产出**: 在管理后台中创建一个新的、可访问的菜单项，并由一个自定义的控制器操作来处理。

---

## 2. 先决条件

- 一个正在运行的 OMS 应用实例。
- 对 MVC 模式有基本了解。
- 有权限修改 `app/miele/desktop.xml` 文件。

---

## 3. 标准执行步骤

### **步骤 1: 在 `desktop.xml` 中定义路由**

路由不是在代码中定义的，而是在 `desktop.xml` 文件中。要创建一个新页面，您必须向 `<menugroup>` 添加一个 `<menu>` 项。

- **操作**: 打开 `app/miele/desktop.xml` 并添加一个新的 `<menu>` 条目。关键属性是：
    - `controller`: 将处理此路由的控制器的名称。框架使用它来查找控制器类。
    - `action`: 控制器中要调用的方法。
    - `permission`: 查看和访问此菜单项所需的权限 ID。
    - `display`: 菜单项是否应在导航中可见。

- **代码示例**: 为 "我的自定义页面" 添加一个新的菜单项。

  ```xml
  <!-- app/miele/desktop.xml -->
  <desktop>
      <workground name="美诺管理" id="miele_manager">
          <menugroup name="物料管理" en="miele-management">
              <!-- ... 现有菜单 ... -->
              <menu controller='admin_my_custom_page' action='index' permission='miele_custom_page_view' display='true' order='10102'>我的自定义页面</menu>
          </menugroup>
      </workground>
  </desktop>
  ```

### **步骤 2: 创建控制器文件和类**

框架使用严格的命名约定将 XML 中的 `controller` 属性映射到 PHP 类文件。

- **约定**: `[app_name]_ctl_[controller_name]`
- **映射**: `controller` 属性 `admin_my_custom_page` 映射到类 `miele_ctl_admin_my_custom_page`。该文件应位于 `app/miele/controller/admin/my/custom/page.php`。

- **操作**: 创建新的控制器文件和类。该类必须扩展 `desktop_controller`。

- **代码示例**:

  ```php
  // app/miele/controller/admin/my/custom/page.php
  <?php

  class miele_ctl_admin_my_custom_page extends desktop_controller
  {
      public function __construct($app)
      {
          parent::__construct($app);
      }

      /**
       * 这是在 XML 中定义的 action 方法。
       */
      public function index()
      {
          // 设置页面标题
          $this->title = '我的自定义页面';

          // 渲染视图模板
          // 路径相对于应用的 'view' 目录
          $this->page('admin/my_custom_page.html');
      }
  }
  ```

### **步骤 3: 创建视图文件**

- **操作**: 创建在控制器的 `page()` 方法中引用的 HTML 模板文件。

- **代码示例**:

  ```html
  <!-- app/miele/view/admin/my_custom_page.html -->
  <h2>欢迎来到我的自定义页面！</h2>
  <p>此内容由 `miele_ctl_admin_my_custom_page` 控制器的 `index` 操作渲染。</p>
  ```

---

## 4. 最佳实践与注意事项

- **命名至关重要**: 框架完全依赖于控制器的命名约定。XML 中的 `admin_my_page` 必须映射到类名中的 `miele_ctl_admin_my_page`。
- **权限**: 对于任何新功能，始终在 `desktop.xml` 的 `<permissions>` 部分定义新权限。
- **基控制器**: 您的控制器应始终调用 `parent::__construct($app)` 以确保基控制器正确初始化。
- **获取输入**: 尽可能使用框架提供的请求对象，而不是全局的 `$_GET` 或 `$_POST`。（需要进一步研究以确定确切的 API）。

---

## 5. 来源与参考

- **源分析**: 对 `app/miele/desktop.xml` 和 `app/miele/controller/admin/sales/material/setmapping.php` 的分析。